"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/training/page",{

/***/ "(app-pages-browser)/./src/app/training/page.tsx":
/*!***********************************!*\
  !*** ./src/app/training/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TrainingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_DocumentUpload__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/DocumentUpload */ \"(app-pages-browser)/./src/components/DocumentUpload.tsx\");\n/* harmony import */ var _components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/TierEnforcement */ \"(app-pages-browser)/./src/components/TierEnforcement/index.ts\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* harmony import */ var _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useConfirmation */ \"(app-pages-browser)/./src/hooks/useConfirmation.ts\");\n/* harmony import */ var _components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/ConfirmationModal */ \"(app-pages-browser)/./src/components/ui/ConfirmationModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction TrainingPage() {\n    _s();\n    // Subscription hook\n    const { subscriptionStatus } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_4__.useSubscription)();\n    const confirmation = (0,_hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_5__.useConfirmation)();\n    // State management\n    const [customConfigs, setCustomConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedConfigId, setSelectedConfigId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [trainingJobs, setTrainingJobs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [documentCount, setDocumentCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Prompt engineering form state\n    const [trainingPrompts, setTrainingPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Load existing training data for a configuration\n    const loadExistingTrainingData = async (configId)=>{\n        if (!configId) return;\n        try {\n            // Load training jobs\n            const jobsResponse = await fetch(\"/api/training/jobs?custom_api_config_id=\".concat(configId));\n            if (jobsResponse.ok) {\n                const jobs = await jobsResponse.json();\n                if (jobs.length > 0) {\n                    var _latestJob_training_data;\n                    const latestJob = jobs[0];\n                    // Load training prompts\n                    if ((_latestJob_training_data = latestJob.training_data) === null || _latestJob_training_data === void 0 ? void 0 : _latestJob_training_data.raw_prompts) {\n                        setTrainingPrompts(latestJob.training_data.raw_prompts);\n                    }\n                }\n            }\n        } catch (err) {\n            console.warn('Failed to load existing training data:', err);\n        }\n    };\n    // Fetch custom API configs on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TrainingPage.useEffect\": ()=>{\n            const fetchConfigs = {\n                \"TrainingPage.useEffect.fetchConfigs\": async ()=>{\n                    try {\n                        const response = await fetch('/api/custom-configs');\n                        if (!response.ok) {\n                            throw new Error('Failed to fetch configurations');\n                        }\n                        const data = await response.json();\n                        setCustomConfigs(data);\n                        if (data.length > 0) {\n                            setSelectedConfigId(data[0].id);\n                            loadExistingTrainingData(data[0].id);\n                        }\n                    } catch (err) {\n                        setError(\"Failed to load configurations: \".concat(err.message));\n                    }\n                }\n            }[\"TrainingPage.useEffect.fetchConfigs\"];\n            fetchConfigs();\n        }\n    }[\"TrainingPage.useEffect\"], []);\n    // Load training data when configuration changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TrainingPage.useEffect\": ()=>{\n            if (selectedConfigId) {\n                loadExistingTrainingData(selectedConfigId);\n            }\n        }\n    }[\"TrainingPage.useEffect\"], [\n        selectedConfigId\n    ]);\n    // Fetch document count for a configuration\n    const fetchDocumentCount = async function(configId) {\n        let retryCount = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        try {\n            // Add cache-busting parameter to ensure fresh data\n            const timestamp = Date.now();\n            const response = await fetch(\"/api/documents/list?configId=\".concat(configId, \"&_t=\").concat(timestamp), {\n                cache: 'no-store' // Ensure we don't get cached responses\n            });\n            if (response.ok) {\n                var _data_documents;\n                const data = await response.json();\n                const newCount = ((_data_documents = data.documents) === null || _data_documents === void 0 ? void 0 : _data_documents.length) || 0;\n                console.log(\"[Training Page] Document count updated: \".concat(newCount, \" for config: \").concat(configId));\n                setDocumentCount(newCount);\n            } else {\n                console.error('Error fetching document count:', response.status, response.statusText);\n                // Retry once if the request failed\n                if (retryCount < 1) {\n                    setTimeout(()=>fetchDocumentCount(configId, retryCount + 1), 1000);\n                }\n            }\n        } catch (err) {\n            console.error('Error fetching document count:', err);\n            // Retry once if there was an error\n            if (retryCount < 1) {\n                setTimeout(()=>fetchDocumentCount(configId, retryCount + 1), 1000);\n            }\n        }\n    };\n    // Fetch document count when config changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TrainingPage.useEffect\": ()=>{\n            if (selectedConfigId) {\n                fetchDocumentCount(selectedConfigId);\n            }\n        }\n    }[\"TrainingPage.useEffect\"], [\n        selectedConfigId\n    ]);\n    // Process training prompts into structured format\n    const processTrainingPrompts = (prompts)=>{\n        const processed = {\n            system_instructions: '',\n            examples: [],\n            behavior_guidelines: '',\n            general_instructions: ''\n        };\n        const lines = prompts.split('\\n').filter((line)=>line.trim());\n        for (const line of lines){\n            const trimmedLine = line.trim();\n            if (trimmedLine.startsWith('SYSTEM:')) {\n                processed.system_instructions += trimmedLine.replace('SYSTEM:', '').trim() + '\\n';\n            } else if (trimmedLine.startsWith('BEHAVIOR:')) {\n                processed.behavior_guidelines += trimmedLine.replace('BEHAVIOR:', '').trim() + '\\n';\n            } else if (trimmedLine.includes('→') || trimmedLine.includes('->')) {\n                // Parse example: \"User input → Expected response\"\n                const separator = trimmedLine.includes('→') ? '→' : '->';\n                const parts = trimmedLine.split(separator);\n                if (parts.length >= 2) {\n                    const input = parts[0].trim();\n                    const output = parts.slice(1).join(separator).trim();\n                    processed.examples.push({\n                        input,\n                        output\n                    });\n                }\n            } else if (trimmedLine.length > 0) {\n                // General instruction\n                processed.general_instructions += trimmedLine + '\\n';\n            }\n        }\n        return processed;\n    };\n    // Handle training job creation or update\n    const handleStartTraining = async ()=>{\n        if (!selectedConfigId || !trainingPrompts.trim()) {\n            setError('Please select an API configuration and provide training prompts.');\n            return;\n        }\n        // Prevent multiple simultaneous training operations\n        if (isLoading) {\n            console.warn('[Training] Operation already in progress, ignoring duplicate request');\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        setSuccessMessage(null);\n        try {\n            var _customConfigs_find;\n            // Process training prompts\n            const processedPrompts = processTrainingPrompts(trainingPrompts);\n            // Generate a meaningful name based on content\n            const configName = ((_customConfigs_find = customConfigs.find((c)=>c.id === selectedConfigId)) === null || _customConfigs_find === void 0 ? void 0 : _customConfigs_find.name) || 'Unknown Config';\n            // Prepare training job data\n            const trainingJobData = {\n                custom_api_config_id: selectedConfigId,\n                name: \"\".concat(configName, \" Training - \").concat(new Date().toLocaleDateString()),\n                description: \"Training job for \".concat(configName, \" with \").concat(processedPrompts.examples.length, \" examples\"),\n                training_data: {\n                    processed_prompts: processedPrompts,\n                    raw_prompts: trainingPrompts.trim(),\n                    last_prompt_update: new Date().toISOString()\n                },\n                parameters: {\n                    training_type: 'prompt_engineering',\n                    created_via: 'training_page',\n                    version: '1.0'\n                }\n            };\n            // Use UPSERT to handle both create and update scenarios\n            const response = await fetch('/api/training/jobs/upsert', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(trainingJobData)\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('[Training] Failed to upsert training job:', errorText);\n                throw new Error(\"Failed to save training job: \".concat(response.status, \" \").concat(errorText));\n            }\n            const result = await response.json();\n            const isUpdate = result.operation === 'updated';\n            console.log(\"[Training] Successfully \".concat(isUpdate ? 'updated' : 'created', \" training job:\"), result.id);\n            // Show success message based on operation type\n            const operationText = isUpdate ? 'updated' : 'enhanced';\n            const operationEmoji = isUpdate ? '🔄' : '🎉';\n            const successMessage = \"\".concat(operationEmoji, \" Prompt Engineering \").concat(isUpdate ? 'updated' : 'completed', \" successfully!\\n\\n\") + 'Your \"'.concat(configName, '\" configuration has been ').concat(operationText, \" with:\\n\") + \"• \".concat(processedPrompts.examples.length, \" training examples\\n\") + \"• Custom system instructions and behavior guidelines\\n\" + \"\\n✨ All future chats using this configuration will automatically:\\n\" + \"• Follow your training examples\\n\" + \"• Apply your behavior guidelines\\n\" + \"• Maintain consistent personality and responses\\n\\n\" + \"\\uD83D\\uDE80 Try it now in the Playground to see your \".concat(isUpdate ? 'updated' : 'enhanced', \" model in action!\\n\\n\") + \"\\uD83D\\uDCA1 Your training prompts remain here so you can modify them anytime.\";\n            setSuccessMessage(successMessage);\n        } catch (err) {\n            console.error('Error in training operation:', err);\n            setError(\"Failed to create prompt engineering: \".concat(err.message));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-800/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-semibold text-white\",\n                                    children: \"Training\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: subscriptionStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__.TierBadge, {\n                                    tier: subscriptionStatus.tier,\n                                    size: \"lg\",\n                                    theme: \"dark\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                lineNumber: 244,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full px-4 sm:px-6 lg:px-8 pb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 bg-red-900/20 border border-red-500/30 rounded-xl p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 text-red-400\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-200 text-sm font-medium\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, this),\n                        successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 bg-green-900/20 border border-green-500/30 rounded-xl p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 text-green-400\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M5 13l4 4L19 7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-200 text-sm font-medium\",\n                                        children: successMessage\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__.TierGuard, {\n                            feature: \"knowledge_base\",\n                            customMessage: \"Knowledge base document upload is available starting with the Professional plan. Upload documents to enhance your AI with proprietary knowledge and create domain-specific assistants.\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-8 mb-8 border border-gray-800/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-orange-500/20 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 text-orange-400\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-white\",\n                                                        children: \"Knowledge Documents\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: \"Upload documents to enhance your AI with proprietary knowledge\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 13\n                                    }, this),\n                                    subscriptionStatus && selectedConfigId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4 bg-gray-800/50 border border-gray-700/50 rounded-lg px-4 py-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__.LimitIndicator, {\n                                            current: documentCount,\n                                            limit: subscriptionStatus.tier === 'professional' ? 5 : subscriptionStatus.tier === 'enterprise' ? 999999 : 0,\n                                            label: \"Knowledge Base Documents\",\n                                            tier: subscriptionStatus.tier,\n                                            showUpgradeHint: true,\n                                            theme: \"dark\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DocumentUpload__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        configId: selectedConfigId,\n                                        theme: \"dark\",\n                                        onDocumentUploaded: ()=>{\n                                            console.log('Document uploaded successfully');\n                                            // Add a small delay to ensure database consistency before refreshing count\n                                            if (selectedConfigId) {\n                                                setTimeout(()=>{\n                                                    fetchDocumentCount(selectedConfigId);\n                                                }, 500); // 500ms delay\n                                            }\n                                        },\n                                        onDocumentDeleted: ()=>{\n                                            console.log('Document deleted successfully');\n                                            // Add a small delay to ensure database consistency before refreshing count\n                                            if (selectedConfigId) {\n                                                setTimeout(()=>{\n                                                    fetchDocumentCount(selectedConfigId);\n                                                }, 500); // 500ms delay\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__.TierGuard, {\n                            feature: \"prompt_engineering\",\n                            customMessage: \"Prompt engineering is available starting with the Starter plan. Create custom prompts to define AI behavior, provide examples, and enhance your model's responses.\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-8 border border-gray-800/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 text-blue-400\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-white\",\n                                                        children: \"Custom Prompts\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: \"Define behavior, examples, and instructions for your AI\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"configSelect\",\n                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                        children: \"Select API Configuration\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"configSelect\",\n                                                        value: selectedConfigId,\n                                                        onChange: (e)=>setSelectedConfigId(e.target.value),\n                                                        className: \"w-full px-4 py-3 border border-gray-700 rounded-xl focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 transition-colors bg-gray-800 text-white text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"Choose which model to train...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            customConfigs.map((config)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: config.id,\n                                                                    children: config.name\n                                                                }, config.id, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                    lineNumber: 377,\n                                                                    columnNumber: 19\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"trainingPrompts\",\n                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                        children: \"Custom Prompts & Instructions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        id: \"trainingPrompts\",\n                                                        value: trainingPrompts,\n                                                        onChange: (e)=>setTrainingPrompts(e.target.value),\n                                                        placeholder: \"Enter your training prompts using these formats:\\n\\nSYSTEM: You are a helpful customer service agent for our company\\nBEHAVIOR: Always be polite and offer solutions\\n\\nUser asks about returns → I'd be happy to help with your return! Let me check our policy for you.\\nCustomer is frustrated → I understand your frustration. Let me see how I can resolve this for you.\\n\\nGeneral instructions can be written as regular text.\",\n                                                        rows: 12,\n                                                        className: \"w-full px-4 py-3 border border-gray-700 rounded-xl focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 transition-colors bg-gray-800 text-white text-sm resize-none font-mono placeholder-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-3 bg-blue-900/20 border border-blue-500/30 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-blue-300 mb-2\",\n                                                                children: \"Training Format Guide:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                lineNumber: 406,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"text-xs text-blue-200 space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"SYSTEM:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                                lineNumber: 408,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            \" Core instructions for the AI's role and personality\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                        lineNumber: 408,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"BEHAVIOR:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                                lineNumber: 409,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            \" Guidelines for how the AI should behave\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                        lineNumber: 409,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"Examples:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                                lineNumber: 410,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            ' Use \"User input → Expected response\" format'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                        lineNumber: 410,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"General:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                                lineNumber: 411,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            \" Any other instructions written as normal text\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                        lineNumber: 411,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                lineNumber: 407,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center pt-6 border-t border-gray-700/50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            className: \"btn-secondary\",\n                                                            onClick: ()=>{\n                                                                confirmation.showConfirmation({\n                                                                    title: 'Clear Training Prompts',\n                                                                    message: 'Are you sure you want to clear all training prompts? This will remove all your custom instructions, examples, and behavior guidelines. This action cannot be undone.',\n                                                                    confirmText: 'Clear All',\n                                                                    cancelText: 'Cancel',\n                                                                    type: 'warning'\n                                                                }, ()=>{\n                                                                    setTrainingPrompts('');\n                                                                });\n                                                            },\n                                                            children: \"Clear Form\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                            lineNumber: 421,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: handleStartTraining,\n                                                        disabled: !selectedConfigId || !trainingPrompts.trim() || isLoading,\n                                                        className: \"btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                    lineNumber: 451,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"Processing Prompts...\"\n                                                            ]\n                                                        }, void 0, true) : 'Save Prompts'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            isOpen: confirmation.isOpen,\n                            onClose: confirmation.hideConfirmation,\n                            onConfirm: confirmation.onConfirm,\n                            title: confirmation.title,\n                            message: confirmation.message,\n                            confirmText: confirmation.confirmText,\n                            cancelText: confirmation.cancelText,\n                            type: confirmation.type,\n                            isLoading: confirmation.isLoading\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                            lineNumber: 464,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                lineNumber: 261,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n        lineNumber: 242,\n        columnNumber: 5\n    }, this);\n}\n_s(TrainingPage, \"UrnJmCfw8iCy300JBOY9o5vKe/8=\", false, function() {\n    return [\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_4__.useSubscription,\n        _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_5__.useConfirmation\n    ];\n});\n_c = TrainingPage;\nvar _c;\n$RefreshReg$(_c, \"TrainingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/training/page.tsx\n"));

/***/ })

});