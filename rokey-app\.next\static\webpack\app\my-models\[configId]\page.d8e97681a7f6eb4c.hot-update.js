"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/[configId]/page",{

/***/ "(app-pages-browser)/./src/components/UserApiKeys/ApiKeyManager.tsx":
/*!******************************************************!*\
  !*** ./src/components/UserApiKeys/ApiKeyManager.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiKeyManager: () => (/* binding */ ApiKeyManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _ApiKeyCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ApiKeyCard */ \"(app-pages-browser)/./src/components/UserApiKeys/ApiKeyCard.tsx\");\n/* harmony import */ var _CreateApiKeyDialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CreateApiKeyDialog */ \"(app-pages-browser)/./src/components/UserApiKeys/CreateApiKeyDialog.tsx\");\n/* harmony import */ var _components_TierEnforcement__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/TierEnforcement */ \"(app-pages-browser)/./src/components/TierEnforcement/index.ts\");\n/* harmony import */ var _components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/ConfirmationModal */ \"(app-pages-browser)/./src/components/ui/ConfirmationModal.tsx\");\n/* harmony import */ var _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/useConfirmation */ \"(app-pages-browser)/./src/hooks/useConfirmation.ts\");\n/* __next_internal_client_entry_do_not_use__ ApiKeyManager auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction ApiKeyManager(param) {\n    let { configId, configName } = param;\n    _s();\n    const [apiKeys, setApiKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [creating, setCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCreateDialog, setShowCreateDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [subscriptionInfo, setSubscriptionInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const confirmation = (0,_hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_9__.useConfirmation)();\n    // Fetch API keys for this configuration\n    const fetchApiKeys = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/user-api-keys?config_id=\".concat(configId));\n            if (!response.ok) {\n                throw new Error('Failed to fetch API keys');\n            }\n            const data = await response.json();\n            setApiKeys(data.api_keys || []);\n        } catch (error) {\n            console.error('Error fetching API keys:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error('Failed to load API keys');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Fetch subscription info\n    const fetchSubscriptionInfo = async (currentKeyCount)=>{\n        try {\n            // Get the tier from the user's active subscription\n            const tierResponse = await fetch('/api/user/subscription-tier');\n            const tierData = tierResponse.ok ? await tierResponse.json() : null;\n            const tier = (tierData === null || tierData === void 0 ? void 0 : tierData.tier) || 'starter';\n            const limits = {\n                free: 3,\n                starter: 50,\n                professional: 999999,\n                enterprise: 999999\n            };\n            // Use provided count or current apiKeys length\n            const keyCount = currentKeyCount !== undefined ? currentKeyCount : apiKeys.length;\n            setSubscriptionInfo({\n                tier,\n                keyLimit: limits[tier] || limits.free,\n                currentCount: keyCount\n            });\n        } catch (error) {\n            console.error('Error fetching subscription info:', error);\n            // Fallback to free tier\n            const keyCount = currentKeyCount !== undefined ? currentKeyCount : apiKeys.length;\n            setSubscriptionInfo({\n                tier: 'free',\n                keyLimit: 3,\n                currentCount: keyCount\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ApiKeyManager.useEffect\": ()=>{\n            fetchApiKeys();\n        }\n    }[\"ApiKeyManager.useEffect\"], [\n        configId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ApiKeyManager.useEffect\": ()=>{\n            if (apiKeys.length >= 0) {\n                fetchSubscriptionInfo();\n            }\n        }\n    }[\"ApiKeyManager.useEffect\"], [\n        apiKeys\n    ]);\n    const handleCreateApiKey = async (keyData)=>{\n        try {\n            setCreating(true);\n            const response = await fetch('/api/user-api-keys', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    ...keyData,\n                    custom_api_config_id: configId\n                })\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                throw new Error(error.error || 'Failed to create API key');\n            }\n            const newApiKey = await response.json();\n            // Show the full API key in a special dialog since it's only shown once\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success('API key created successfully!');\n            // Optimistically add the new key to the list immediately\n            const optimisticKey = {\n                ...newApiKey,\n                // Add any missing fields that might be needed for display\n                custom_api_configs: {\n                    id: configId,\n                    name: configName\n                }\n            };\n            setApiKeys((prevKeys)=>{\n                const newKeys = [\n                    optimisticKey,\n                    ...prevKeys\n                ];\n                // Immediately update subscription info with new count\n                fetchSubscriptionInfo(newKeys.length);\n                return newKeys;\n            });\n            // Also refresh to ensure we have the latest data and correct counts\n            await fetchApiKeys();\n            return newApiKey;\n        } catch (error) {\n            console.error('Error creating API key:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(error.message || 'Failed to create API key');\n            throw error;\n        } finally{\n            setCreating(false);\n        }\n    };\n    const handleRevokeApiKey = async (keyId)=>{\n        const apiKey = apiKeys.find((key)=>key.id === keyId);\n        const keyName = (apiKey === null || apiKey === void 0 ? void 0 : apiKey.key_name) || 'this API key';\n        confirmation.showConfirmation({\n            title: 'Revoke API Key',\n            message: 'Are you sure you want to revoke \"'.concat(keyName, '\"? This action cannot be undone and will immediately disable the key.'),\n            confirmText: 'Revoke Key',\n            cancelText: 'Cancel',\n            type: 'danger'\n        }, async ()=>{\n            try {\n                const response = await fetch(\"/api/user-api-keys/\".concat(keyId), {\n                    method: 'DELETE'\n                });\n                if (!response.ok) {\n                    const error = await response.json();\n                    throw new Error(error.error || 'Failed to revoke API key');\n                }\n                // Update the key status to revoked immediately after successful API call\n                setApiKeys((prevKeys)=>prevKeys.map((key)=>key.id === keyId ? {\n                            ...key,\n                            status: 'revoked'\n                        } : key));\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success('API key revoked successfully');\n            } catch (error) {\n                console.error('Error revoking API key:', error);\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(error.message || 'Failed to revoke API key');\n                throw error; // Re-throw to let the confirmation modal handle the error state\n            }\n        });\n    };\n    const handleCreateDialogClose = (open)=>{\n        setShowCreateDialog(open);\n    // Note: We no longer need to refresh here since we refresh immediately after creation\n    };\n    const canCreateMoreKeys = subscriptionInfo ? subscriptionInfo.currentCount < subscriptionInfo.keyLimit : true;\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-6 w-6 animate-spin mr-2 text-gray-400\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-400\",\n                        children: \"Loading API keys...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 208,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n            lineNumber: 207,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"API Keys\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: [\n                                    \"Generate API keys for programmatic access to \",\n                                    configName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this),\n                    canCreateMoreKeys ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: ()=>setShowCreateDialog(true),\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this),\n                            \"Create API Key\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-end gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                disabled: true,\n                                className: \"flex items-center gap-2 opacity-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Create API Key\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-orange-600 font-medium\",\n                                children: (subscriptionInfo === null || subscriptionInfo === void 0 ? void 0 : subscriptionInfo.tier) === 'free' ? 'Upgrade to Starter plan for more API keys' : 'API key limit reached - upgrade for unlimited keys'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            subscriptionInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 border border-gray-200 rounded-lg px-4 py-2 mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_7__.LimitIndicator, {\n                    current: subscriptionInfo.currentCount,\n                    limit: subscriptionInfo.keyLimit,\n                    label: \"User-Generated API Keys\",\n                    tier: subscriptionInfo.tier,\n                    showUpgradeHint: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 258,\n                columnNumber: 9\n            }, this),\n            apiKeys.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"text-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            className: \"h-12 w-12 mx-auto text-gray-400 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-2\",\n                            children: \"No API Keys\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Create your first API key to start using the RouKey API programmatically.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 13\n                        }, this),\n                        canCreateMoreKeys ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: ()=>setShowCreateDialog(true),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 17\n                                }, this),\n                                \"Create Your First API Key\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    disabled: true,\n                                    className: \"opacity-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Create Your First API Key\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-orange-600 font-medium\",\n                                    children: (subscriptionInfo === null || subscriptionInfo === void 0 ? void 0 : subscriptionInfo.tier) === 'free' ? 'Upgrade to Starter plan to create API keys' : 'API key limit reached - upgrade for unlimited keys'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 271,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4\",\n                children: apiKeys.map((apiKey)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ApiKeyCard__WEBPACK_IMPORTED_MODULE_5__.ApiKeyCard, {\n                        apiKey: apiKey,\n                        onRevoke: handleRevokeApiKey\n                    }, apiKey.id, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 302,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateApiKeyDialog__WEBPACK_IMPORTED_MODULE_6__.CreateApiKeyDialog, {\n                open: showCreateDialog,\n                onOpenChange: handleCreateDialogClose,\n                onCreateApiKey: handleCreateApiKey,\n                configName: configName,\n                creating: creating,\n                subscriptionTier: (subscriptionInfo === null || subscriptionInfo === void 0 ? void 0 : subscriptionInfo.tier) || 'starter'\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 314,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: confirmation.isOpen,\n                onClose: confirmation.hideConfirmation,\n                onConfirm: confirmation.onConfirm,\n                title: confirmation.title,\n                message: confirmation.message,\n                confirmText: confirmation.confirmText,\n                cancelText: confirmation.cancelText,\n                type: confirmation.type,\n                isLoading: confirmation.isLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 324,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, this);\n}\n_s(ApiKeyManager, \"wzx+xNoPucKu2oL+bFaGPsR3hPI=\", false, function() {\n    return [\n        _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_9__.useConfirmation\n    ];\n});\n_c = ApiKeyManager;\nvar _c;\n$RefreshReg$(_c, \"ApiKeyManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/UserApiKeys/ApiKeyManager.tsx\n"));

/***/ })

});