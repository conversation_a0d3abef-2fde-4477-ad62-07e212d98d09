"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/training/page",{

/***/ "(app-pages-browser)/./src/components/TierEnforcement/TierGuard.tsx":
/*!******************************************************!*\
  !*** ./src/components/TierEnforcement/TierGuard.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TierGuard: () => (/* binding */ TierGuard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* harmony import */ var _lib_stripe_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/stripe-client */ \"(app-pages-browser)/./src/lib/stripe-client.ts\");\n/* harmony import */ var _UpgradePrompt__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./UpgradePrompt */ \"(app-pages-browser)/./src/components/TierEnforcement/UpgradePrompt.tsx\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(app-pages-browser)/./src/components/ui/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ TierGuard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n/**\n * TierGuard component that conditionally renders content based on user's subscription tier\n * Shows upgrade prompt if user doesn't have access to the feature\n */ function TierGuard(param) {\n    let { feature, children, fallback, showUpgradePrompt = true, customMessage, currentCount = 0, theme = 'light' } = param;\n    _s();\n    const { subscriptionStatus, loading } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_2__.useSubscription)();\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\TierGuard.tsx\",\n            lineNumber: 35,\n            columnNumber: 12\n        }, this);\n    }\n    const userTier = (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) || 'free';\n    // Special handling for configurations feature - check count-based limits\n    if (feature === 'configurations') {\n        const tierConfig = (0,_lib_stripe_client__WEBPACK_IMPORTED_MODULE_3__.getTierConfig)(userTier);\n        const hasAccess = currentCount < tierConfig.limits.configurations;\n        if (hasAccess) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: children\n            }, void 0, false);\n        }\n    } else {\n        // For other features, use the standard feature access check\n        const hasAccess = (0,_lib_stripe_client__WEBPACK_IMPORTED_MODULE_3__.hasFeatureAccess)(userTier, feature);\n        if (hasAccess) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: children\n            }, void 0, false);\n        }\n    }\n    if (fallback) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: fallback\n        }, void 0, false);\n    }\n    if (showUpgradePrompt) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UpgradePrompt__WEBPACK_IMPORTED_MODULE_4__.UpgradePrompt, {\n            feature: feature,\n            currentTier: userTier,\n            customMessage: customMessage,\n            theme: theme\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\TierGuard.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, this);\n    }\n    return null;\n}\n_s(TierGuard, \"pNLyuVLtm5kqKyXgAauRVqtJUQ4=\", false, function() {\n    return [\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_2__.useSubscription\n    ];\n});\n_c = TierGuard;\nvar _c;\n$RefreshReg$(_c, \"TierGuard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TierEnforcement/TierGuard.tsx\n"));

/***/ })

});