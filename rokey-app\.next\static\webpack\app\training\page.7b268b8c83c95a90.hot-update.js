"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/training/page",{

/***/ "(app-pages-browser)/./src/components/DocumentUpload.tsx":
/*!*******************************************!*\
  !*** ./src/components/DocumentUpload.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DocumentUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useConfirmation */ \"(app-pages-browser)/./src/hooks/useConfirmation.ts\");\n/* harmony import */ var _components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/ConfirmationModal */ \"(app-pages-browser)/./src/components/ui/ConfirmationModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction DocumentUpload(param) {\n    let { configId, onDocumentUploaded, onDocumentDeleted, theme = 'light' } = param;\n    _s();\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadProgress, setUploadProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [dragActive, setDragActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const confirmation = (0,_hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_2__.useConfirmation)();\n    // Load documents for the current config with retry logic\n    const loadDocuments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentUpload.useCallback[loadDocuments]\": async function() {\n            let retryCount = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, expectedDocumentId = arguments.length > 1 ? arguments[1] : void 0;\n            if (!configId) return;\n            if (retryCount === 0) {\n                setIsRefreshing(true);\n            }\n            try {\n                const response = await fetch(\"/api/documents/list?configId=\".concat(configId));\n                if (response.ok) {\n                    const data = await response.json();\n                    const newDocuments = data.documents || [];\n                    // If we're looking for a specific document and it's not found, retry\n                    if (expectedDocumentId && retryCount < 3) {\n                        const foundDocument = newDocuments.find({\n                            \"DocumentUpload.useCallback[loadDocuments].foundDocument\": (doc)=>doc.id === expectedDocumentId\n                        }[\"DocumentUpload.useCallback[loadDocuments].foundDocument\"]);\n                        if (!foundDocument) {\n                            console.log(\"[DocumentUpload] Document \".concat(expectedDocumentId, \" not found, retrying in \").concat((retryCount + 1) * 500, \"ms...\"));\n                            setTimeout({\n                                \"DocumentUpload.useCallback[loadDocuments]\": ()=>{\n                                    loadDocuments(retryCount + 1, expectedDocumentId);\n                                }\n                            }[\"DocumentUpload.useCallback[loadDocuments]\"], (retryCount + 1) * 500); // 500ms, 1s, 1.5s delays\n                            return;\n                        }\n                    }\n                    // Smart merge: preserve optimistic updates until server confirms them\n                    setDocuments({\n                        \"DocumentUpload.useCallback[loadDocuments]\": (prev)=>{\n                            // If we're looking for a specific document (after upload), handle optimistic updates\n                            if (expectedDocumentId) {\n                                const serverDoc = newDocuments.find({\n                                    \"DocumentUpload.useCallback[loadDocuments].serverDoc\": (doc)=>doc.id === expectedDocumentId\n                                }[\"DocumentUpload.useCallback[loadDocuments].serverDoc\"]);\n                                if (serverDoc) {\n                                    // Server has the document, use server data (it's authoritative)\n                                    console.log(\"[DocumentUpload] Server confirmed document \".concat(expectedDocumentId, \", using server data\"));\n                                    return newDocuments.sort({\n                                        \"DocumentUpload.useCallback[loadDocuments]\": (a, b)=>new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\n                                    }[\"DocumentUpload.useCallback[loadDocuments]\"]);\n                                } else {\n                                    // Server doesn't have the document yet, preserve optimistic version\n                                    console.log(\"[DocumentUpload] Server doesn't have document \".concat(expectedDocumentId, \" yet, preserving optimistic version\"));\n                                    const serverDocIds = new Set(newDocuments.map({\n                                        \"DocumentUpload.useCallback[loadDocuments]\": (doc)=>doc.id\n                                    }[\"DocumentUpload.useCallback[loadDocuments]\"]));\n                                    const optimisticDocs = prev.filter({\n                                        \"DocumentUpload.useCallback[loadDocuments].optimisticDocs\": (doc)=>!serverDocIds.has(doc.id)\n                                    }[\"DocumentUpload.useCallback[loadDocuments].optimisticDocs\"]);\n                                    const allDocs = [\n                                        ...newDocuments,\n                                        ...optimisticDocs\n                                    ];\n                                    return allDocs.sort({\n                                        \"DocumentUpload.useCallback[loadDocuments]\": (a, b)=>new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\n                                    }[\"DocumentUpload.useCallback[loadDocuments]\"]);\n                                }\n                            } else {\n                                // Regular refresh (page load, config change), just use server data\n                                return newDocuments.sort({\n                                    \"DocumentUpload.useCallback[loadDocuments]\": (a, b)=>new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\n                                }[\"DocumentUpload.useCallback[loadDocuments]\"]);\n                            }\n                        }\n                    }[\"DocumentUpload.useCallback[loadDocuments]\"]);\n                }\n            } catch (err) {\n                console.error('Failed to load documents:', err);\n                // Retry on error if we haven't exceeded retry count\n                if (retryCount < 2) {\n                    setTimeout({\n                        \"DocumentUpload.useCallback[loadDocuments]\": ()=>{\n                            loadDocuments(retryCount + 1, expectedDocumentId);\n                        }\n                    }[\"DocumentUpload.useCallback[loadDocuments]\"], 1000);\n                }\n            } finally{\n                if (retryCount === 0 || expectedDocumentId) {\n                    setIsRefreshing(false);\n                }\n            }\n        }\n    }[\"DocumentUpload.useCallback[loadDocuments]\"], [\n        configId\n    ]);\n    // Load documents when configId changes\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"DocumentUpload.useEffect\": ()=>{\n            loadDocuments();\n        }\n    }[\"DocumentUpload.useEffect\"], [\n        loadDocuments\n    ]);\n    // Handle file upload\n    const handleFileUpload = async (files)=>{\n        if (!configId) {\n            setError('Please select an API configuration first');\n            return;\n        }\n        const file = files[0];\n        if (!file) return;\n        // Validate file type\n        const allowedTypes = [\n            'application/pdf',\n            'text/plain',\n            'text/markdown'\n        ];\n        if (!allowedTypes.includes(file.type)) {\n            setError('Please upload PDF, TXT, or MD files only');\n            return;\n        }\n        // Validate file size (10MB max)\n        if (file.size > 10 * 1024 * 1024) {\n            setError('File size must be less than 10MB');\n            return;\n        }\n        setIsUploading(true);\n        setError(null);\n        setSuccess(null);\n        setUploadProgress(0);\n        try {\n            const formData = new FormData();\n            formData.append('file', file);\n            formData.append('configId', configId);\n            // Simulate progress for better UX\n            const progressInterval = setInterval(()=>{\n                setUploadProgress((prev)=>Math.min(prev + 10, 90));\n            }, 200);\n            const response = await fetch('/api/documents/upload', {\n                method: 'POST',\n                body: formData\n            });\n            clearInterval(progressInterval);\n            setUploadProgress(100);\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || 'Upload failed');\n            }\n            const result = await response.json();\n            console.log(\"[DocumentUpload] Upload successful:\", result);\n            setSuccess(\"✨ \".concat(file.name, \" uploaded successfully! Processing \").concat(result.document.chunks_total, \" chunks.\"));\n            // Optimistically add the document to the list immediately\n            const optimisticDocument = {\n                id: result.document.id,\n                filename: result.document.filename || file.name,\n                file_type: file.type,\n                file_size: file.size,\n                status: result.document.status || 'processing',\n                chunks_count: result.document.chunks_processed || 0,\n                created_at: new Date().toISOString()\n            };\n            console.log(\"[DocumentUpload] Adding optimistic document:\", optimisticDocument);\n            setDocuments((prev)=>{\n                // Check if document already exists (shouldn't happen, but safety check)\n                const exists = prev.find((doc)=>doc.id === optimisticDocument.id);\n                if (exists) {\n                    console.log(\"[DocumentUpload] Document \".concat(optimisticDocument.id, \" already exists, updating instead\"));\n                    return prev.map((doc)=>doc.id === optimisticDocument.id ? optimisticDocument : doc);\n                }\n                return [\n                    optimisticDocument,\n                    ...prev\n                ];\n            });\n            // Small delay to allow database transaction to commit, then reload with retry logic\n            setTimeout(async ()=>{\n                console.log(\"[DocumentUpload] Starting document list refresh for document: \".concat(result.document.id));\n                await loadDocuments(0, result.document.id);\n            }, 200);\n            // Call callback if provided\n            onDocumentUploaded === null || onDocumentUploaded === void 0 ? void 0 : onDocumentUploaded();\n        } catch (err) {\n            const errorMessage = \"Upload failed: \".concat(err.message);\n            setError(errorMessage);\n            // Auto-clear error message after 8 seconds\n            setTimeout(()=>setError(null), 8000);\n        } finally{\n            setIsUploading(false);\n            setUploadProgress(0);\n            // Clear file input\n            if (fileInputRef.current) {\n                fileInputRef.current.value = '';\n            }\n            // Auto-clear success message after 5 seconds\n            if (success) {\n                setTimeout(()=>setSuccess(null), 5000);\n            }\n        }\n    };\n    // Handle drag and drop\n    const handleDrag = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentUpload.useCallback[handleDrag]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            if (e.type === 'dragenter' || e.type === 'dragover') {\n                setDragActive(true);\n            } else if (e.type === 'dragleave') {\n                setDragActive(false);\n            }\n        }\n    }[\"DocumentUpload.useCallback[handleDrag]\"], []);\n    const handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentUpload.useCallback[handleDrop]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            setDragActive(false);\n            if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n                handleFileUpload(e.dataTransfer.files);\n            }\n        }\n    }[\"DocumentUpload.useCallback[handleDrop]\"], [\n        configId\n    ]);\n    // Handle file input change\n    const handleInputChange = (e)=>{\n        if (e.target.files && e.target.files[0]) {\n            handleFileUpload(e.target.files);\n        }\n    };\n    // Delete document\n    const handleDeleteDocument = (documentId, documentName)=>{\n        confirmation.showConfirmation({\n            title: 'Delete Document',\n            message: 'Are you sure you want to delete \"'.concat(documentName, '\"? This will permanently remove the document and all its processed chunks from your knowledge base. This action cannot be undone.'),\n            confirmText: 'Delete Document',\n            cancelText: 'Cancel',\n            type: 'danger'\n        }, async ()=>{\n            // Optimistically remove the document from the list\n            const originalDocuments = documents;\n            setDocuments((prev)=>prev.filter((doc)=>doc.id !== documentId));\n            try {\n                console.log(\"[DocumentUpload] Attempting to delete document: \".concat(documentId));\n                const response = await fetch(\"/api/documents/\".concat(documentId), {\n                    method: 'DELETE'\n                });\n                if (!response.ok) {\n                    const errorData = await response.json().catch(()=>({\n                            error: 'Unknown error'\n                        }));\n                    console.error(\"[DocumentUpload] Delete failed:\", response.status, errorData);\n                    // Restore the original list on error\n                    setDocuments(originalDocuments);\n                    throw new Error(errorData.error || \"HTTP \".concat(response.status, \": Failed to delete document\"));\n                }\n                const result = await response.json();\n                console.log(\"[DocumentUpload] Delete successful:\", result);\n                setSuccess('Document deleted successfully');\n                // Force refresh the document list to ensure consistency\n                setTimeout(async ()=>{\n                    console.log(\"[DocumentUpload] Refreshing document list after deletion\");\n                    await loadDocuments();\n                }, 200);\n                // Call callback if provided\n                onDocumentDeleted === null || onDocumentDeleted === void 0 ? void 0 : onDocumentDeleted();\n                // Auto-clear success message after 3 seconds\n                setTimeout(()=>setSuccess(null), 3000);\n            } catch (err) {\n                console.error(\"[DocumentUpload] Delete error:\", err);\n                // Restore the original list on error\n                setDocuments(originalDocuments);\n                const errorMessage = \"Delete failed: \".concat(err.message);\n                setError(errorMessage);\n                // Auto-clear error message after 8 seconds\n                setTimeout(()=>setError(null), 8000);\n            }\n        });\n    };\n    // Format file size\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    // Get file icon\n    const getFileIcon = (fileType)=>{\n        if (fileType.includes('pdf')) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"w-5 h-5 text-red-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n            lineNumber: 319,\n            columnNumber: 42\n        }, this);\n        if (fileType.includes('word')) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"w-5 h-5 text-blue-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n            lineNumber: 320,\n            columnNumber: 43\n        }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"w-5 h-5 text-gray-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n            lineNumber: 321,\n            columnNumber: 12\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"rounded-xl p-4 \".concat(theme === 'dark' ? 'bg-red-900/20 border border-red-500/30' : 'bg-red-50 border border-red-200'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-5 h-5 \".concat(theme === 'dark' ? 'text-red-400' : 'text-red-600')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm font-medium \".concat(theme === 'dark' ? 'text-red-200' : 'text-red-800'),\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                    lineNumber: 333,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 328,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"rounded-xl p-4 \".concat(theme === 'dark' ? 'bg-green-900/20 border border-green-500/30' : 'bg-green-50 border border-green-200'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"w-5 h-5 \".concat(theme === 'dark' ? 'text-green-400' : 'text-green-600')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm font-medium \".concat(theme === 'dark' ? 'text-green-200' : 'text-green-800'),\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 348,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                    lineNumber: 346,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 341,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative border-2 border-dashed rounded-2xl p-8 text-center transition-all duration-300 transform \".concat(dragActive ? theme === 'dark' ? 'border-orange-400 bg-orange-900/20 scale-105 shadow-lg' : 'border-orange-400 bg-orange-50 scale-105 shadow-lg' : theme === 'dark' ? 'border-gray-600 hover:border-orange-400 hover:bg-orange-900/10 hover:scale-102 hover:shadow-md' : 'border-gray-300 hover:border-orange-400 hover:bg-orange-50 hover:scale-102 hover:shadow-md', \" \").concat(!configId ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'),\n                onDragEnter: handleDrag,\n                onDragLeave: handleDrag,\n                onDragOver: handleDrag,\n                onDrop: handleDrop,\n                onClick: ()=>{\n                    var _fileInputRef_current;\n                    return configId && ((_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click());\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: fileInputRef,\n                        type: \"file\",\n                        className: \"hidden\",\n                        accept: \".pdf,.txt,.md\",\n                        onChange: handleInputChange,\n                        disabled: !configId || isUploading\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 9\n                    }, this),\n                    isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-12 h-12 text-orange-500 mx-auto animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-medium \".concat(theme === 'dark' ? 'text-white' : 'text-gray-900'),\n                                        children: \"Processing Document...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full rounded-full h-2 \".concat(theme === 'dark' ? 'bg-gray-700' : 'bg-gray-200'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-orange-500 h-2 rounded-full transition-all duration-300\",\n                                            style: {\n                                                width: \"\".concat(uploadProgress, \"%\")\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm \".concat(theme === 'dark' ? 'text-gray-300' : 'text-gray-600'),\n                                        children: [\n                                            uploadProgress,\n                                            \"% complete\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-12 h-12 mx-auto \".concat(theme === 'dark' ? 'text-gray-500' : 'text-gray-400')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-medium \".concat(theme === 'dark' ? 'text-white' : 'text-gray-900'),\n                                        children: configId ? 'Upload Knowledge Documents' : 'Select a configuration first'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm mt-1 \".concat(theme === 'dark' ? 'text-gray-300' : 'text-gray-600'),\n                                        children: \"Drag and drop files here, or click to browse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs mt-2 \".concat(theme === 'dark' ? 'text-gray-400' : 'text-gray-500'),\n                                        children: \"Supports PDF, TXT, MD files up to 10MB\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 394,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 354,\n                columnNumber: 7\n            }, this),\n            documents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold \".concat(theme === 'dark' ? 'text-white' : 'text-gray-900'),\n                                children: \"Uploaded Documents\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 13\n                            }, this),\n                            isRefreshing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-sm \".concat(theme === 'dark' ? 'text-gray-300' : 'text-gray-600'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Refreshing...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-4\",\n                        children: documents.map((doc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 rounded-xl transition-shadow \".concat(theme === 'dark' ? 'bg-gray-800/50 border border-gray-700/50 hover:shadow-lg hover:shadow-gray-900/20' : 'bg-white border border-gray-200 hover:shadow-md'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            getFileIcon(doc.file_type),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: doc.filename\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            formatFileSize(doc.file_size),\n                                                            \" • \",\n                                                            doc.chunks_count,\n                                                            \" chunks\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    doc.status === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-5 h-5 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    doc.status === 'processing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-5 h-5 text-orange-500 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    doc.status === 'failed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-5 h-5 text-red-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium \".concat(doc.status === 'completed' ? 'text-green-600' : doc.status === 'processing' ? 'text-orange-600' : 'text-red-600'),\n                                                        children: doc.status === 'completed' ? 'Ready' : doc.status === 'processing' ? 'Processing' : 'Failed'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleDeleteDocument(doc.id, doc.filename),\n                                                className: \"p-1 text-gray-400 hover:text-red-500 transition-colors\",\n                                                title: \"Delete document\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, doc.id, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 413,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: confirmation.isOpen,\n                onClose: confirmation.hideConfirmation,\n                onConfirm: confirmation.onConfirm,\n                title: confirmation.title,\n                message: confirmation.message,\n                confirmText: confirmation.confirmText,\n                cancelText: confirmation.cancelText,\n                type: confirmation.type,\n                isLoading: confirmation.isLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 480,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n        lineNumber: 325,\n        columnNumber: 5\n    }, this);\n}\n_s(DocumentUpload, \"qy7mnAkmgJ7Ofmiub+gVBhPvEic=\", false, function() {\n    return [\n        _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_2__.useConfirmation\n    ];\n});\n_c = DocumentUpload;\nvar _c;\n$RefreshReg$(_c, \"DocumentUpload\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DocumentUpload.tsx\n"));

/***/ })

});