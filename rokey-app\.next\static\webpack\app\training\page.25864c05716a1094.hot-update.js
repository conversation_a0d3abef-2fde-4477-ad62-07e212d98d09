"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/training/page",{

/***/ "(app-pages-browser)/./src/components/DocumentUpload.tsx":
/*!*******************************************!*\
  !*** ./src/components/DocumentUpload.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DocumentUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useConfirmation */ \"(app-pages-browser)/./src/hooks/useConfirmation.ts\");\n/* harmony import */ var _components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/ConfirmationModal */ \"(app-pages-browser)/./src/components/ui/ConfirmationModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction DocumentUpload(param) {\n    let { configId, onDocumentUploaded, onDocumentDeleted, theme = 'light' } = param;\n    _s();\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadProgress, setUploadProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [dragActive, setDragActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const confirmation = (0,_hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_2__.useConfirmation)();\n    // Load documents for the current config with retry logic\n    const loadDocuments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentUpload.useCallback[loadDocuments]\": async function() {\n            let retryCount = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, expectedDocumentId = arguments.length > 1 ? arguments[1] : void 0;\n            if (!configId) return;\n            if (retryCount === 0) {\n                setIsRefreshing(true);\n            }\n            try {\n                const response = await fetch(\"/api/documents/list?configId=\".concat(configId));\n                if (response.ok) {\n                    const data = await response.json();\n                    const newDocuments = data.documents || [];\n                    // If we're looking for a specific document and it's not found, retry\n                    if (expectedDocumentId && retryCount < 3) {\n                        const foundDocument = newDocuments.find({\n                            \"DocumentUpload.useCallback[loadDocuments].foundDocument\": (doc)=>doc.id === expectedDocumentId\n                        }[\"DocumentUpload.useCallback[loadDocuments].foundDocument\"]);\n                        if (!foundDocument) {\n                            console.log(\"[DocumentUpload] Document \".concat(expectedDocumentId, \" not found, retrying in \").concat((retryCount + 1) * 500, \"ms...\"));\n                            setTimeout({\n                                \"DocumentUpload.useCallback[loadDocuments]\": ()=>{\n                                    loadDocuments(retryCount + 1, expectedDocumentId);\n                                }\n                            }[\"DocumentUpload.useCallback[loadDocuments]\"], (retryCount + 1) * 500); // 500ms, 1s, 1.5s delays\n                            return;\n                        }\n                    }\n                    // Smart merge: preserve optimistic updates until server confirms them\n                    setDocuments({\n                        \"DocumentUpload.useCallback[loadDocuments]\": (prev)=>{\n                            // If we're looking for a specific document (after upload), handle optimistic updates\n                            if (expectedDocumentId) {\n                                const serverDoc = newDocuments.find({\n                                    \"DocumentUpload.useCallback[loadDocuments].serverDoc\": (doc)=>doc.id === expectedDocumentId\n                                }[\"DocumentUpload.useCallback[loadDocuments].serverDoc\"]);\n                                if (serverDoc) {\n                                    // Server has the document, use server data (it's authoritative)\n                                    console.log(\"[DocumentUpload] Server confirmed document \".concat(expectedDocumentId, \", using server data\"));\n                                    return newDocuments.sort({\n                                        \"DocumentUpload.useCallback[loadDocuments]\": (a, b)=>new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\n                                    }[\"DocumentUpload.useCallback[loadDocuments]\"]);\n                                } else {\n                                    // Server doesn't have the document yet, preserve optimistic version\n                                    console.log(\"[DocumentUpload] Server doesn't have document \".concat(expectedDocumentId, \" yet, preserving optimistic version\"));\n                                    const serverDocIds = new Set(newDocuments.map({\n                                        \"DocumentUpload.useCallback[loadDocuments]\": (doc)=>doc.id\n                                    }[\"DocumentUpload.useCallback[loadDocuments]\"]));\n                                    const optimisticDocs = prev.filter({\n                                        \"DocumentUpload.useCallback[loadDocuments].optimisticDocs\": (doc)=>!serverDocIds.has(doc.id)\n                                    }[\"DocumentUpload.useCallback[loadDocuments].optimisticDocs\"]);\n                                    const allDocs = [\n                                        ...newDocuments,\n                                        ...optimisticDocs\n                                    ];\n                                    return allDocs.sort({\n                                        \"DocumentUpload.useCallback[loadDocuments]\": (a, b)=>new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\n                                    }[\"DocumentUpload.useCallback[loadDocuments]\"]);\n                                }\n                            } else {\n                                // Regular refresh (page load, config change), just use server data\n                                return newDocuments.sort({\n                                    \"DocumentUpload.useCallback[loadDocuments]\": (a, b)=>new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\n                                }[\"DocumentUpload.useCallback[loadDocuments]\"]);\n                            }\n                        }\n                    }[\"DocumentUpload.useCallback[loadDocuments]\"]);\n                }\n            } catch (err) {\n                console.error('Failed to load documents:', err);\n                // Retry on error if we haven't exceeded retry count\n                if (retryCount < 2) {\n                    setTimeout({\n                        \"DocumentUpload.useCallback[loadDocuments]\": ()=>{\n                            loadDocuments(retryCount + 1, expectedDocumentId);\n                        }\n                    }[\"DocumentUpload.useCallback[loadDocuments]\"], 1000);\n                }\n            } finally{\n                if (retryCount === 0 || expectedDocumentId) {\n                    setIsRefreshing(false);\n                }\n            }\n        }\n    }[\"DocumentUpload.useCallback[loadDocuments]\"], [\n        configId\n    ]);\n    // Load documents when configId changes\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"DocumentUpload.useEffect\": ()=>{\n            loadDocuments();\n        }\n    }[\"DocumentUpload.useEffect\"], [\n        loadDocuments\n    ]);\n    // Handle file upload\n    const handleFileUpload = async (files)=>{\n        if (!configId) {\n            setError('Please select an API configuration first');\n            return;\n        }\n        const file = files[0];\n        if (!file) return;\n        // Validate file type\n        const allowedTypes = [\n            'application/pdf',\n            'text/plain',\n            'text/markdown'\n        ];\n        if (!allowedTypes.includes(file.type)) {\n            setError('Please upload PDF, TXT, or MD files only');\n            return;\n        }\n        // Validate file size (10MB max)\n        if (file.size > 10 * 1024 * 1024) {\n            setError('File size must be less than 10MB');\n            return;\n        }\n        setIsUploading(true);\n        setError(null);\n        setSuccess(null);\n        setUploadProgress(0);\n        try {\n            const formData = new FormData();\n            formData.append('file', file);\n            formData.append('configId', configId);\n            // Simulate progress for better UX\n            const progressInterval = setInterval(()=>{\n                setUploadProgress((prev)=>Math.min(prev + 10, 90));\n            }, 200);\n            const response = await fetch('/api/documents/upload', {\n                method: 'POST',\n                body: formData\n            });\n            clearInterval(progressInterval);\n            setUploadProgress(100);\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || 'Upload failed');\n            }\n            const result = await response.json();\n            console.log(\"[DocumentUpload] Upload successful:\", result);\n            setSuccess(\"✨ \".concat(file.name, \" uploaded successfully! Processing \").concat(result.document.chunks_total, \" chunks.\"));\n            // Optimistically add the document to the list immediately\n            const optimisticDocument = {\n                id: result.document.id,\n                filename: result.document.filename || file.name,\n                file_type: file.type,\n                file_size: file.size,\n                status: result.document.status || 'processing',\n                chunks_count: result.document.chunks_processed || 0,\n                created_at: new Date().toISOString()\n            };\n            console.log(\"[DocumentUpload] Adding optimistic document:\", optimisticDocument);\n            setDocuments((prev)=>{\n                // Check if document already exists (shouldn't happen, but safety check)\n                const exists = prev.find((doc)=>doc.id === optimisticDocument.id);\n                if (exists) {\n                    console.log(\"[DocumentUpload] Document \".concat(optimisticDocument.id, \" already exists, updating instead\"));\n                    return prev.map((doc)=>doc.id === optimisticDocument.id ? optimisticDocument : doc);\n                }\n                return [\n                    optimisticDocument,\n                    ...prev\n                ];\n            });\n            // Small delay to allow database transaction to commit, then reload with retry logic\n            setTimeout(async ()=>{\n                console.log(\"[DocumentUpload] Starting document list refresh for document: \".concat(result.document.id));\n                await loadDocuments(0, result.document.id);\n            }, 200);\n            // Call callback if provided\n            onDocumentUploaded === null || onDocumentUploaded === void 0 ? void 0 : onDocumentUploaded();\n        } catch (err) {\n            const errorMessage = \"Upload failed: \".concat(err.message);\n            setError(errorMessage);\n            // Auto-clear error message after 8 seconds\n            setTimeout(()=>setError(null), 8000);\n        } finally{\n            setIsUploading(false);\n            setUploadProgress(0);\n            // Clear file input\n            if (fileInputRef.current) {\n                fileInputRef.current.value = '';\n            }\n            // Auto-clear success message after 5 seconds\n            if (success) {\n                setTimeout(()=>setSuccess(null), 5000);\n            }\n        }\n    };\n    // Handle drag and drop\n    const handleDrag = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentUpload.useCallback[handleDrag]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            if (e.type === 'dragenter' || e.type === 'dragover') {\n                setDragActive(true);\n            } else if (e.type === 'dragleave') {\n                setDragActive(false);\n            }\n        }\n    }[\"DocumentUpload.useCallback[handleDrag]\"], []);\n    const handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentUpload.useCallback[handleDrop]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            setDragActive(false);\n            if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n                handleFileUpload(e.dataTransfer.files);\n            }\n        }\n    }[\"DocumentUpload.useCallback[handleDrop]\"], [\n        configId\n    ]);\n    // Handle file input change\n    const handleInputChange = (e)=>{\n        if (e.target.files && e.target.files[0]) {\n            handleFileUpload(e.target.files);\n        }\n    };\n    // Delete document\n    const handleDeleteDocument = (documentId, documentName)=>{\n        confirmation.showConfirmation({\n            title: 'Delete Document',\n            message: 'Are you sure you want to delete \"'.concat(documentName, '\"? This will permanently remove the document and all its processed chunks from your knowledge base. This action cannot be undone.'),\n            confirmText: 'Delete Document',\n            cancelText: 'Cancel',\n            type: 'danger'\n        }, async ()=>{\n            // Optimistically remove the document from the list\n            const originalDocuments = documents;\n            setDocuments((prev)=>prev.filter((doc)=>doc.id !== documentId));\n            try {\n                console.log(\"[DocumentUpload] Attempting to delete document: \".concat(documentId));\n                const response = await fetch(\"/api/documents/\".concat(documentId), {\n                    method: 'DELETE'\n                });\n                if (!response.ok) {\n                    const errorData = await response.json().catch(()=>({\n                            error: 'Unknown error'\n                        }));\n                    console.error(\"[DocumentUpload] Delete failed:\", response.status, errorData);\n                    // Restore the original list on error\n                    setDocuments(originalDocuments);\n                    throw new Error(errorData.error || \"HTTP \".concat(response.status, \": Failed to delete document\"));\n                }\n                const result = await response.json();\n                console.log(\"[DocumentUpload] Delete successful:\", result);\n                setSuccess('Document deleted successfully');\n                // Force refresh the document list to ensure consistency\n                setTimeout(async ()=>{\n                    console.log(\"[DocumentUpload] Refreshing document list after deletion\");\n                    await loadDocuments();\n                }, 200);\n                // Call callback if provided\n                onDocumentDeleted === null || onDocumentDeleted === void 0 ? void 0 : onDocumentDeleted();\n                // Auto-clear success message after 3 seconds\n                setTimeout(()=>setSuccess(null), 3000);\n            } catch (err) {\n                console.error(\"[DocumentUpload] Delete error:\", err);\n                // Restore the original list on error\n                setDocuments(originalDocuments);\n                const errorMessage = \"Delete failed: \".concat(err.message);\n                setError(errorMessage);\n                // Auto-clear error message after 8 seconds\n                setTimeout(()=>setError(null), 8000);\n            }\n        });\n    };\n    // Format file size\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    // Get file icon\n    const getFileIcon = (fileType)=>{\n        if (fileType.includes('pdf')) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"w-5 h-5 text-red-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n            lineNumber: 319,\n            columnNumber: 42\n        }, this);\n        if (fileType.includes('word')) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"w-5 h-5 text-blue-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n            lineNumber: 320,\n            columnNumber: 43\n        }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"w-5 h-5 text-gray-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n            lineNumber: 321,\n            columnNumber: 12\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"rounded-xl p-4 \".concat(theme === 'dark' ? 'bg-red-900/20 border border-red-500/30' : 'bg-red-50 border border-red-200'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-5 h-5 \".concat(theme === 'dark' ? 'text-red-400' : 'text-red-600')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm font-medium \".concat(theme === 'dark' ? 'text-red-200' : 'text-red-800'),\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                    lineNumber: 333,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 328,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"rounded-xl p-4 \".concat(theme === 'dark' ? 'bg-green-900/20 border border-green-500/30' : 'bg-green-50 border border-green-200'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"w-5 h-5 \".concat(theme === 'dark' ? 'text-green-400' : 'text-green-600')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm font-medium \".concat(theme === 'dark' ? 'text-green-200' : 'text-green-800'),\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 348,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                    lineNumber: 346,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 341,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative border-2 border-dashed rounded-2xl p-8 text-center transition-all duration-300 transform \".concat(dragActive ? theme === 'dark' ? 'border-orange-400 bg-orange-900/20 scale-105 shadow-lg' : 'border-orange-400 bg-orange-50 scale-105 shadow-lg' : theme === 'dark' ? 'border-gray-600 hover:border-orange-400 hover:bg-orange-900/10 hover:scale-102 hover:shadow-md' : 'border-gray-300 hover:border-orange-400 hover:bg-orange-50 hover:scale-102 hover:shadow-md', \" \").concat(!configId ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'),\n                onDragEnter: handleDrag,\n                onDragLeave: handleDrag,\n                onDragOver: handleDrag,\n                onDrop: handleDrop,\n                onClick: ()=>{\n                    var _fileInputRef_current;\n                    return configId && ((_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click());\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: fileInputRef,\n                        type: \"file\",\n                        className: \"hidden\",\n                        accept: \".pdf,.txt,.md\",\n                        onChange: handleInputChange,\n                        disabled: !configId || isUploading\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 9\n                    }, this),\n                    isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-12 h-12 text-orange-500 mx-auto animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-medium \".concat(theme === 'dark' ? 'text-white' : 'text-gray-900'),\n                                        children: \"Processing Document...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full rounded-full h-2 \".concat(theme === 'dark' ? 'bg-gray-700' : 'bg-gray-200'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-orange-500 h-2 rounded-full transition-all duration-300\",\n                                            style: {\n                                                width: \"\".concat(uploadProgress, \"%\")\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm \".concat(theme === 'dark' ? 'text-gray-300' : 'text-gray-600'),\n                                        children: [\n                                            uploadProgress,\n                                            \"% complete\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-12 h-12 mx-auto \".concat(theme === 'dark' ? 'text-gray-500' : 'text-gray-400')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-medium \".concat(theme === 'dark' ? 'text-white' : 'text-gray-900'),\n                                        children: configId ? 'Upload Knowledge Documents' : 'Select a configuration first'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm mt-1 \".concat(theme === 'dark' ? 'text-gray-300' : 'text-gray-600'),\n                                        children: \"Drag and drop files here, or click to browse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs mt-2 \".concat(theme === 'dark' ? 'text-gray-400' : 'text-gray-500'),\n                                        children: \"Supports PDF, TXT, MD files up to 10MB\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 394,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 354,\n                columnNumber: 7\n            }, this),\n            documents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold \".concat(theme === 'dark' ? 'text-white' : 'text-gray-900'),\n                                children: \"Uploaded Documents\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 13\n                            }, this),\n                            isRefreshing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-sm \".concat(theme === 'dark' ? 'text-gray-300' : 'text-gray-600'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Refreshing...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-4\",\n                        children: documents.map((doc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 rounded-xl transition-shadow \".concat(theme === 'dark' ? 'bg-gray-800/50 border border-gray-700/50 hover:shadow-lg hover:shadow-gray-900/20' : 'bg-white border border-gray-200 hover:shadow-md'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            getFileIcon(doc.file_type),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium \".concat(theme === 'dark' ? 'text-white' : 'text-gray-900'),\n                                                        children: doc.filename\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm \".concat(theme === 'dark' ? 'text-gray-300' : 'text-gray-600'),\n                                                        children: [\n                                                            formatFileSize(doc.file_size),\n                                                            \" • \",\n                                                            doc.chunks_count,\n                                                            \" chunks\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    doc.status === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-5 h-5 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    doc.status === 'processing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-5 h-5 text-orange-500 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    doc.status === 'failed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-5 h-5 text-red-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium \".concat(doc.status === 'completed' ? 'text-green-600' : doc.status === 'processing' ? 'text-orange-600' : 'text-red-600'),\n                                                        children: doc.status === 'completed' ? 'Ready' : doc.status === 'processing' ? 'Processing' : 'Failed'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleDeleteDocument(doc.id, doc.filename),\n                                                className: \"p-1 transition-colors \".concat(theme === 'dark' ? 'text-gray-500 hover:text-red-400' : 'text-gray-400 hover:text-red-500'),\n                                                title: \"Delete document\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, doc.id, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 413,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: confirmation.isOpen,\n                onClose: confirmation.hideConfirmation,\n                onConfirm: confirmation.onConfirm,\n                title: confirmation.title,\n                message: confirmation.message,\n                confirmText: confirmation.confirmText,\n                cancelText: confirmation.cancelText,\n                type: confirmation.type,\n                isLoading: confirmation.isLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 484,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n        lineNumber: 325,\n        columnNumber: 5\n    }, this);\n}\n_s(DocumentUpload, \"qy7mnAkmgJ7Ofmiub+gVBhPvEic=\", false, function() {\n    return [\n        _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_2__.useConfirmation\n    ];\n});\n_c = DocumentUpload;\nvar _c;\n$RefreshReg$(_c, \"DocumentUpload\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DocumentUpload.tsx\n"));

/***/ })

});