"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/training/page",{

/***/ "(app-pages-browser)/./src/components/TierEnforcement/TierGuard.tsx":
/*!******************************************************!*\
  !*** ./src/components/TierEnforcement/TierGuard.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TierGuard: () => (/* binding */ TierGuard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* harmony import */ var _lib_stripe_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/stripe-client */ \"(app-pages-browser)/./src/lib/stripe-client.ts\");\n/* harmony import */ var _UpgradePrompt__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./UpgradePrompt */ \"(app-pages-browser)/./src/components/TierEnforcement/UpgradePrompt.tsx\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(app-pages-browser)/./src/components/ui/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ TierGuard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n/**\n * TierGuard component that conditionally renders content based on user's subscription tier\n * Shows upgrade prompt if user doesn't have access to the feature\n */ function TierGuard(param) {\n    let { feature, children, fallback, showUpgradePrompt = true, customMessage, currentCount = 0 } = param;\n    _s();\n    const { subscriptionStatus, loading } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_2__.useSubscription)();\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\TierGuard.tsx\",\n            lineNumber: 34,\n            columnNumber: 12\n        }, this);\n    }\n    const userTier = (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) || 'free';\n    // Special handling for configurations feature - check count-based limits\n    if (feature === 'configurations') {\n        const tierConfig = (0,_lib_stripe_client__WEBPACK_IMPORTED_MODULE_3__.getTierConfig)(userTier);\n        const hasAccess = currentCount < tierConfig.limits.configurations;\n        if (hasAccess) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: children\n            }, void 0, false);\n        }\n    } else {\n        // For other features, use the standard feature access check\n        const hasAccess = (0,_lib_stripe_client__WEBPACK_IMPORTED_MODULE_3__.hasFeatureAccess)(userTier, feature);\n        if (hasAccess) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: children\n            }, void 0, false);\n        }\n    }\n    if (fallback) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: fallback\n        }, void 0, false);\n    }\n    if (showUpgradePrompt) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UpgradePrompt__WEBPACK_IMPORTED_MODULE_4__.UpgradePrompt, {\n            feature: feature,\n            currentTier: userTier,\n            customMessage: customMessage\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\TierGuard.tsx\",\n            lineNumber: 62,\n            columnNumber: 7\n        }, this);\n    }\n    return null;\n}\n_s(TierGuard, \"pNLyuVLtm5kqKyXgAauRVqtJUQ4=\", false, function() {\n    return [\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_2__.useSubscription\n    ];\n});\n_c = TierGuard;\nvar _c;\n$RefreshReg$(_c, \"TierGuard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TierEnforcement/TierGuard.tsx\n"));

/***/ })

});