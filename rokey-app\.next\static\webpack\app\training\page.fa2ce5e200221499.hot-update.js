"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/training/page",{

/***/ "(app-pages-browser)/./src/components/DocumentUpload.tsx":
/*!*******************************************!*\
  !*** ./src/components/DocumentUpload.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DocumentUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useConfirmation */ \"(app-pages-browser)/./src/hooks/useConfirmation.ts\");\n/* harmony import */ var _components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/ConfirmationModal */ \"(app-pages-browser)/./src/components/ui/ConfirmationModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction DocumentUpload(param) {\n    let { configId, onDocumentUploaded, onDocumentDeleted, theme = 'light' } = param;\n    _s();\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadProgress, setUploadProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [dragActive, setDragActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const confirmation = (0,_hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_2__.useConfirmation)();\n    // Load documents for the current config with retry logic\n    const loadDocuments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentUpload.useCallback[loadDocuments]\": async function() {\n            let retryCount = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, expectedDocumentId = arguments.length > 1 ? arguments[1] : void 0;\n            if (!configId) return;\n            if (retryCount === 0) {\n                setIsRefreshing(true);\n            }\n            try {\n                const response = await fetch(\"/api/documents/list?configId=\".concat(configId));\n                if (response.ok) {\n                    const data = await response.json();\n                    const newDocuments = data.documents || [];\n                    // If we're looking for a specific document and it's not found, retry\n                    if (expectedDocumentId && retryCount < 3) {\n                        const foundDocument = newDocuments.find({\n                            \"DocumentUpload.useCallback[loadDocuments].foundDocument\": (doc)=>doc.id === expectedDocumentId\n                        }[\"DocumentUpload.useCallback[loadDocuments].foundDocument\"]);\n                        if (!foundDocument) {\n                            console.log(\"[DocumentUpload] Document \".concat(expectedDocumentId, \" not found, retrying in \").concat((retryCount + 1) * 500, \"ms...\"));\n                            setTimeout({\n                                \"DocumentUpload.useCallback[loadDocuments]\": ()=>{\n                                    loadDocuments(retryCount + 1, expectedDocumentId);\n                                }\n                            }[\"DocumentUpload.useCallback[loadDocuments]\"], (retryCount + 1) * 500); // 500ms, 1s, 1.5s delays\n                            return;\n                        }\n                    }\n                    // Smart merge: preserve optimistic updates until server confirms them\n                    setDocuments({\n                        \"DocumentUpload.useCallback[loadDocuments]\": (prev)=>{\n                            // If we're looking for a specific document (after upload), handle optimistic updates\n                            if (expectedDocumentId) {\n                                const serverDoc = newDocuments.find({\n                                    \"DocumentUpload.useCallback[loadDocuments].serverDoc\": (doc)=>doc.id === expectedDocumentId\n                                }[\"DocumentUpload.useCallback[loadDocuments].serverDoc\"]);\n                                if (serverDoc) {\n                                    // Server has the document, use server data (it's authoritative)\n                                    console.log(\"[DocumentUpload] Server confirmed document \".concat(expectedDocumentId, \", using server data\"));\n                                    return newDocuments.sort({\n                                        \"DocumentUpload.useCallback[loadDocuments]\": (a, b)=>new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\n                                    }[\"DocumentUpload.useCallback[loadDocuments]\"]);\n                                } else {\n                                    // Server doesn't have the document yet, preserve optimistic version\n                                    console.log(\"[DocumentUpload] Server doesn't have document \".concat(expectedDocumentId, \" yet, preserving optimistic version\"));\n                                    const serverDocIds = new Set(newDocuments.map({\n                                        \"DocumentUpload.useCallback[loadDocuments]\": (doc)=>doc.id\n                                    }[\"DocumentUpload.useCallback[loadDocuments]\"]));\n                                    const optimisticDocs = prev.filter({\n                                        \"DocumentUpload.useCallback[loadDocuments].optimisticDocs\": (doc)=>!serverDocIds.has(doc.id)\n                                    }[\"DocumentUpload.useCallback[loadDocuments].optimisticDocs\"]);\n                                    const allDocs = [\n                                        ...newDocuments,\n                                        ...optimisticDocs\n                                    ];\n                                    return allDocs.sort({\n                                        \"DocumentUpload.useCallback[loadDocuments]\": (a, b)=>new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\n                                    }[\"DocumentUpload.useCallback[loadDocuments]\"]);\n                                }\n                            } else {\n                                // Regular refresh (page load, config change), just use server data\n                                return newDocuments.sort({\n                                    \"DocumentUpload.useCallback[loadDocuments]\": (a, b)=>new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\n                                }[\"DocumentUpload.useCallback[loadDocuments]\"]);\n                            }\n                        }\n                    }[\"DocumentUpload.useCallback[loadDocuments]\"]);\n                }\n            } catch (err) {\n                console.error('Failed to load documents:', err);\n                // Retry on error if we haven't exceeded retry count\n                if (retryCount < 2) {\n                    setTimeout({\n                        \"DocumentUpload.useCallback[loadDocuments]\": ()=>{\n                            loadDocuments(retryCount + 1, expectedDocumentId);\n                        }\n                    }[\"DocumentUpload.useCallback[loadDocuments]\"], 1000);\n                }\n            } finally{\n                if (retryCount === 0 || expectedDocumentId) {\n                    setIsRefreshing(false);\n                }\n            }\n        }\n    }[\"DocumentUpload.useCallback[loadDocuments]\"], [\n        configId\n    ]);\n    // Load documents when configId changes\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"DocumentUpload.useEffect\": ()=>{\n            loadDocuments();\n        }\n    }[\"DocumentUpload.useEffect\"], [\n        loadDocuments\n    ]);\n    // Handle file upload\n    const handleFileUpload = async (files)=>{\n        if (!configId) {\n            setError('Please select an API configuration first');\n            return;\n        }\n        const file = files[0];\n        if (!file) return;\n        // Validate file type\n        const allowedTypes = [\n            'application/pdf',\n            'text/plain',\n            'text/markdown'\n        ];\n        if (!allowedTypes.includes(file.type)) {\n            setError('Please upload PDF, TXT, or MD files only');\n            return;\n        }\n        // Validate file size (10MB max)\n        if (file.size > 10 * 1024 * 1024) {\n            setError('File size must be less than 10MB');\n            return;\n        }\n        setIsUploading(true);\n        setError(null);\n        setSuccess(null);\n        setUploadProgress(0);\n        try {\n            const formData = new FormData();\n            formData.append('file', file);\n            formData.append('configId', configId);\n            // Simulate progress for better UX\n            const progressInterval = setInterval(()=>{\n                setUploadProgress((prev)=>Math.min(prev + 10, 90));\n            }, 200);\n            const response = await fetch('/api/documents/upload', {\n                method: 'POST',\n                body: formData\n            });\n            clearInterval(progressInterval);\n            setUploadProgress(100);\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || 'Upload failed');\n            }\n            const result = await response.json();\n            console.log(\"[DocumentUpload] Upload successful:\", result);\n            setSuccess(\"✨ \".concat(file.name, \" uploaded successfully! Processing \").concat(result.document.chunks_total, \" chunks.\"));\n            // Optimistically add the document to the list immediately\n            const optimisticDocument = {\n                id: result.document.id,\n                filename: result.document.filename || file.name,\n                file_type: file.type,\n                file_size: file.size,\n                status: result.document.status || 'processing',\n                chunks_count: result.document.chunks_processed || 0,\n                created_at: new Date().toISOString()\n            };\n            console.log(\"[DocumentUpload] Adding optimistic document:\", optimisticDocument);\n            setDocuments((prev)=>{\n                // Check if document already exists (shouldn't happen, but safety check)\n                const exists = prev.find((doc)=>doc.id === optimisticDocument.id);\n                if (exists) {\n                    console.log(\"[DocumentUpload] Document \".concat(optimisticDocument.id, \" already exists, updating instead\"));\n                    return prev.map((doc)=>doc.id === optimisticDocument.id ? optimisticDocument : doc);\n                }\n                return [\n                    optimisticDocument,\n                    ...prev\n                ];\n            });\n            // Small delay to allow database transaction to commit, then reload with retry logic\n            setTimeout(async ()=>{\n                console.log(\"[DocumentUpload] Starting document list refresh for document: \".concat(result.document.id));\n                await loadDocuments(0, result.document.id);\n            }, 200);\n            // Call callback if provided\n            onDocumentUploaded === null || onDocumentUploaded === void 0 ? void 0 : onDocumentUploaded();\n        } catch (err) {\n            const errorMessage = \"Upload failed: \".concat(err.message);\n            setError(errorMessage);\n            // Auto-clear error message after 8 seconds\n            setTimeout(()=>setError(null), 8000);\n        } finally{\n            setIsUploading(false);\n            setUploadProgress(0);\n            // Clear file input\n            if (fileInputRef.current) {\n                fileInputRef.current.value = '';\n            }\n            // Auto-clear success message after 5 seconds\n            if (success) {\n                setTimeout(()=>setSuccess(null), 5000);\n            }\n        }\n    };\n    // Handle drag and drop\n    const handleDrag = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentUpload.useCallback[handleDrag]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            if (e.type === 'dragenter' || e.type === 'dragover') {\n                setDragActive(true);\n            } else if (e.type === 'dragleave') {\n                setDragActive(false);\n            }\n        }\n    }[\"DocumentUpload.useCallback[handleDrag]\"], []);\n    const handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentUpload.useCallback[handleDrop]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            setDragActive(false);\n            if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n                handleFileUpload(e.dataTransfer.files);\n            }\n        }\n    }[\"DocumentUpload.useCallback[handleDrop]\"], [\n        configId\n    ]);\n    // Handle file input change\n    const handleInputChange = (e)=>{\n        if (e.target.files && e.target.files[0]) {\n            handleFileUpload(e.target.files);\n        }\n    };\n    // Delete document\n    const handleDeleteDocument = (documentId, documentName)=>{\n        confirmation.showConfirmation({\n            title: 'Delete Document',\n            message: 'Are you sure you want to delete \"'.concat(documentName, '\"? This will permanently remove the document and all its processed chunks from your knowledge base. This action cannot be undone.'),\n            confirmText: 'Delete Document',\n            cancelText: 'Cancel',\n            type: 'danger'\n        }, async ()=>{\n            // Optimistically remove the document from the list\n            const originalDocuments = documents;\n            setDocuments((prev)=>prev.filter((doc)=>doc.id !== documentId));\n            try {\n                console.log(\"[DocumentUpload] Attempting to delete document: \".concat(documentId));\n                const response = await fetch(\"/api/documents/\".concat(documentId), {\n                    method: 'DELETE'\n                });\n                if (!response.ok) {\n                    const errorData = await response.json().catch(()=>({\n                            error: 'Unknown error'\n                        }));\n                    console.error(\"[DocumentUpload] Delete failed:\", response.status, errorData);\n                    // Restore the original list on error\n                    setDocuments(originalDocuments);\n                    throw new Error(errorData.error || \"HTTP \".concat(response.status, \": Failed to delete document\"));\n                }\n                const result = await response.json();\n                console.log(\"[DocumentUpload] Delete successful:\", result);\n                setSuccess('Document deleted successfully');\n                // Force refresh the document list to ensure consistency\n                setTimeout(async ()=>{\n                    console.log(\"[DocumentUpload] Refreshing document list after deletion\");\n                    await loadDocuments();\n                }, 200);\n                // Call callback if provided\n                onDocumentDeleted === null || onDocumentDeleted === void 0 ? void 0 : onDocumentDeleted();\n                // Auto-clear success message after 3 seconds\n                setTimeout(()=>setSuccess(null), 3000);\n            } catch (err) {\n                console.error(\"[DocumentUpload] Delete error:\", err);\n                // Restore the original list on error\n                setDocuments(originalDocuments);\n                const errorMessage = \"Delete failed: \".concat(err.message);\n                setError(errorMessage);\n                // Auto-clear error message after 8 seconds\n                setTimeout(()=>setError(null), 8000);\n            }\n        });\n    };\n    // Format file size\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    // Get file icon\n    const getFileIcon = (fileType)=>{\n        if (fileType.includes('pdf')) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"w-5 h-5 text-red-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n            lineNumber: 319,\n            columnNumber: 42\n        }, this);\n        if (fileType.includes('word')) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"w-5 h-5 text-blue-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n            lineNumber: 320,\n            columnNumber: 43\n        }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"w-5 h-5 text-gray-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n            lineNumber: 321,\n            columnNumber: 12\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"rounded-xl p-4 \".concat(theme === 'dark' ? 'bg-red-900/20 border border-red-500/30' : 'bg-red-50 border border-red-200'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-5 h-5 \".concat(theme === 'dark' ? 'text-red-400' : 'text-red-600')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm font-medium \".concat(theme === 'dark' ? 'text-red-200' : 'text-red-800'),\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                    lineNumber: 333,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 328,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"rounded-xl p-4 \".concat(theme === 'dark' ? 'bg-green-900/20 border border-green-500/30' : 'bg-green-50 border border-green-200'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"w-5 h-5 \".concat(theme === 'dark' ? 'text-green-400' : 'text-green-600')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm font-medium \".concat(theme === 'dark' ? 'text-green-200' : 'text-green-800'),\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 348,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                    lineNumber: 346,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 341,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative border-2 border-dashed rounded-2xl p-8 text-center transition-all duration-300 transform \".concat(dragActive ? theme === 'dark' ? 'border-orange-400 bg-orange-900/20 scale-105 shadow-lg' : 'border-orange-400 bg-orange-50 scale-105 shadow-lg' : theme === 'dark' ? 'border-gray-600 hover:border-orange-400 hover:bg-orange-900/10 hover:scale-102 hover:shadow-md' : 'border-gray-300 hover:border-orange-400 hover:bg-orange-50 hover:scale-102 hover:shadow-md', \" \").concat(!configId ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'),\n                onDragEnter: handleDrag,\n                onDragLeave: handleDrag,\n                onDragOver: handleDrag,\n                onDrop: handleDrop,\n                onClick: ()=>{\n                    var _fileInputRef_current;\n                    return configId && ((_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click());\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: fileInputRef,\n                        type: \"file\",\n                        className: \"hidden\",\n                        accept: \".pdf,.txt,.md\",\n                        onChange: handleInputChange,\n                        disabled: !configId || isUploading\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 9\n                    }, this),\n                    isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-12 h-12 text-orange-500 mx-auto animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-medium \".concat(theme === 'dark' ? 'text-white' : 'text-gray-900'),\n                                        children: \"Processing Document...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full rounded-full h-2 \".concat(theme === 'dark' ? 'bg-gray-700' : 'bg-gray-200'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-orange-500 h-2 rounded-full transition-all duration-300\",\n                                            style: {\n                                                width: \"\".concat(uploadProgress, \"%\")\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm \".concat(theme === 'dark' ? 'text-gray-300' : 'text-gray-600'),\n                                        children: [\n                                            uploadProgress,\n                                            \"% complete\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-12 h-12 mx-auto \".concat(theme === 'dark' ? 'text-gray-500' : 'text-gray-400')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-medium \".concat(theme === 'dark' ? 'text-white' : 'text-gray-900'),\n                                        children: configId ? 'Upload Knowledge Documents' : 'Select a configuration first'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm mt-1 \".concat(theme === 'dark' ? 'text-gray-300' : 'text-gray-600'),\n                                        children: \"Drag and drop files here, or click to browse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs mt-2 \".concat(theme === 'dark' ? 'text-gray-400' : 'text-gray-500'),\n                                        children: \"Supports PDF, TXT, MD files up to 10MB\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 394,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 354,\n                columnNumber: 7\n            }, this),\n            documents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold \".concat(theme === 'dark' ? 'text-white' : 'text-gray-900'),\n                                children: \"Uploaded Documents\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 13\n                            }, this),\n                            isRefreshing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-sm \".concat(theme === 'dark' ? 'text-gray-300' : 'text-gray-600'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Refreshing...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-4\",\n                        children: documents.map((doc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 rounded-xl transition-shadow \".concat(theme === 'dark' ? 'bg-gray-800/50 border border-gray-700/50 hover:shadow-lg hover:shadow-gray-900/20' : 'bg-white border border-gray-200 hover:shadow-md'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            getFileIcon(doc.file_type),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium \".concat(theme === 'dark' ? 'text-white' : 'text-gray-900'),\n                                                        children: doc.filename\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm \".concat(theme === 'dark' ? 'text-gray-300' : 'text-gray-600'),\n                                                        children: [\n                                                            formatFileSize(doc.file_size),\n                                                            \" • \",\n                                                            doc.chunks_count,\n                                                            \" chunks\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    doc.status === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-5 h-5 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    doc.status === 'processing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-5 h-5 text-orange-500 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    doc.status === 'failed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-5 h-5 text-red-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium \".concat(doc.status === 'completed' ? 'text-green-600' : doc.status === 'processing' ? 'text-orange-600' : 'text-red-600'),\n                                                        children: doc.status === 'completed' ? 'Ready' : doc.status === 'processing' ? 'Processing' : 'Failed'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleDeleteDocument(doc.id, doc.filename),\n                                                className: \"p-1 text-gray-400 hover:text-red-500 transition-colors\",\n                                                title: \"Delete document\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, doc.id, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 413,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: confirmation.isOpen,\n                onClose: confirmation.hideConfirmation,\n                onConfirm: confirmation.onConfirm,\n                title: confirmation.title,\n                message: confirmation.message,\n                confirmText: confirmation.confirmText,\n                cancelText: confirmation.cancelText,\n                type: confirmation.type,\n                isLoading: confirmation.isLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 480,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n        lineNumber: 325,\n        columnNumber: 5\n    }, this);\n}\n_s(DocumentUpload, \"qy7mnAkmgJ7Ofmiub+gVBhPvEic=\", false, function() {\n    return [\n        _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_2__.useConfirmation\n    ];\n});\n_c = DocumentUpload;\nvar _c;\n$RefreshReg$(_c, \"DocumentUpload\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DocumentUpload.tsx\n"));

/***/ })

});