"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BeakerIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BeakerIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BeakerIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BeakerIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BeakerIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowTrendingUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BeakerIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BeakerIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BeakerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BeakerIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BeakerIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BeakerIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction DashboardPage() {\n    var _user_user_metadata, _user_user_metadata_full_name, _user_user_metadata1;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__.useSubscription)();\n    const [analyticsData, setAnalyticsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Start with false for progressive loading\n    const [initialLoad, setInitialLoad] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [recentActivity, setRecentActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [systemStatus, setSystemStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            name: 'API Gateway',\n            status: 'operational'\n        },\n        {\n            name: 'Routing Engine',\n            status: 'operational'\n        },\n        {\n            name: 'Analytics',\n            status: 'degraded'\n        }\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            // Progressive loading: render UI first, then load data\n            const loadData = {\n                \"DashboardPage.useEffect.loadData\": async ()=>{\n                    // Small delay to allow UI to render first\n                    await new Promise({\n                        \"DashboardPage.useEffect.loadData\": (resolve)=>setTimeout(resolve, 50)\n                    }[\"DashboardPage.useEffect.loadData\"]);\n                    // Load data in parallel for better performance\n                    const promises = [\n                        fetchAnalyticsData(),\n                        fetchRecentActivity(),\n                        checkSystemStatus()\n                    ];\n                    await Promise.allSettled(promises);\n                    setInitialLoad(false);\n                }\n            }[\"DashboardPage.useEffect.loadData\"];\n            loadData();\n            // Set up auto-refresh for activity feed every 30 seconds\n            const activityInterval = setInterval(fetchRecentActivity, 30000);\n            // Set up system status check every 60 seconds\n            const statusInterval = setInterval(checkSystemStatus, 60000);\n            return ({\n                \"DashboardPage.useEffect\": ()=>{\n                    clearInterval(activityInterval);\n                    clearInterval(statusInterval);\n                }\n            })[\"DashboardPage.useEffect\"];\n        }\n    }[\"DashboardPage.useEffect\"], []);\n    const fetchAnalyticsData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DashboardPage.useCallback[fetchAnalyticsData]\": async ()=>{\n            try {\n                // Only show loading on initial load, not on refreshes\n                if (initialLoad) {\n                    setLoading(true);\n                }\n                // Get data for the last 30 days\n                const thirtyDaysAgo = new Date();\n                thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n                const response = await fetch(\"/api/analytics/summary?startDate=\".concat(thirtyDaysAgo.toISOString(), \"&groupBy=day\"));\n                if (!response.ok) {\n                    throw new Error('Failed to fetch analytics data');\n                }\n                const data = await response.json();\n                setAnalyticsData(data);\n            } catch (err) {\n                setError(err.message);\n                console.error('Error fetching analytics:', err);\n            } finally{\n                if (initialLoad) {\n                    setLoading(false);\n                }\n            }\n        }\n    }[\"DashboardPage.useCallback[fetchAnalyticsData]\"], [\n        initialLoad\n    ]);\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat('en-US', {\n            style: 'currency',\n            currency: 'USD',\n            minimumFractionDigits: 2,\n            maximumFractionDigits: 6\n        }).format(amount);\n    };\n    const formatNumber = (num)=>{\n        return new Intl.NumberFormat('en-US').format(num);\n    };\n    const fetchRecentActivity = async ()=>{\n        try {\n            // Fetch recent activity from the new activity API\n            const response = await fetch('/api/activity?limit=10');\n            if (!response.ok) {\n                throw new Error('Failed to fetch recent activity');\n            }\n            const data = await response.json();\n            const activities = data.activities.map((activity)=>({\n                    id: activity.id,\n                    action: activity.action,\n                    model: activity.model,\n                    time: activity.time,\n                    status: activity.status,\n                    details: activity.details\n                }));\n            setRecentActivity(activities);\n        } catch (err) {\n            console.error('Error fetching recent activity:', err);\n            // Set fallback activity data\n            setRecentActivity([\n                {\n                    id: '1',\n                    action: 'System initialized',\n                    model: 'RoKey',\n                    time: 'Just now',\n                    status: 'info'\n                }\n            ]);\n        }\n    };\n    const checkSystemStatus = async ()=>{\n        try {\n            const response = await fetch('/api/system-status');\n            if (!response.ok) {\n                throw new Error('Failed to fetch system status');\n            }\n            const data = await response.json();\n            const statusItems = data.checks.map((check)=>({\n                    name: check.name,\n                    status: check.status,\n                    lastChecked: new Date(check.lastChecked).toLocaleTimeString()\n                }));\n            setSystemStatus(statusItems);\n        } catch (err) {\n            console.error('Error checking system status:', err);\n            // Set fallback status\n            setSystemStatus([\n                {\n                    name: 'API Gateway',\n                    status: 'down',\n                    lastChecked: new Date().toLocaleTimeString()\n                },\n                {\n                    name: 'Routing Engine',\n                    status: 'down',\n                    lastChecked: new Date().toLocaleTimeString()\n                },\n                {\n                    name: 'Analytics',\n                    status: 'down',\n                    lastChecked: new Date().toLocaleTimeString()\n                }\n            ]);\n        }\n    };\n    const getTimeAgo = (date)=>{\n        const now = new Date();\n        const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n        if (diffInSeconds < 60) return 'Just now';\n        if (diffInSeconds < 3600) return \"\".concat(Math.floor(diffInSeconds / 60), \" minutes ago\");\n        if (diffInSeconds < 86400) return \"\".concat(Math.floor(diffInSeconds / 3600), \" hours ago\");\n        return \"\".concat(Math.floor(diffInSeconds / 86400), \" days ago\");\n    };\n    // Quick Actions handlers\n    const handleAddNewModel = ()=>{\n        router.push('/my-models');\n    };\n    const handleTestPlayground = ()=>{\n        router.push('/playground');\n    };\n    const handleViewLogs = ()=>{\n        router.push('/logs');\n    };\n    // Get user's first name for welcome message\n    const firstName = (user === null || user === void 0 ? void 0 : (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.first_name) || (user === null || user === void 0 ? void 0 : (_user_user_metadata1 = user.user_metadata) === null || _user_user_metadata1 === void 0 ? void 0 : (_user_user_metadata_full_name = _user_user_metadata1.full_name) === null || _user_user_metadata_full_name === void 0 ? void 0 : _user_user_metadata_full_name.split(' ')[0]) || 'there';\n    const stats = analyticsData ? [\n        {\n            name: 'Total Requests',\n            value: formatNumber(analyticsData.summary.total_requests),\n            change: 'Last 30 days',\n            changeType: 'neutral',\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            name: 'Total Cost',\n            value: formatCurrency(analyticsData.summary.total_cost),\n            change: \"\".concat(formatCurrency(analyticsData.summary.average_cost_per_request), \" avg/request\"),\n            changeType: 'neutral',\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            name: 'Success Rate',\n            value: \"\".concat(analyticsData.summary.success_rate.toFixed(1), \"%\"),\n            change: \"\".concat(formatNumber(analyticsData.summary.successful_requests), \" successful\"),\n            changeType: analyticsData.summary.success_rate >= 95 ? 'positive' : 'negative',\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            name: 'Total Tokens',\n            value: formatNumber(analyticsData.summary.total_tokens),\n            change: \"\".concat(formatNumber(analyticsData.summary.total_input_tokens), \" in, \").concat(formatNumber(analyticsData.summary.total_output_tokens), \" out\"),\n            changeType: 'neutral',\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        }\n    ] : [];\n    // Show loading only on initial load, not on subsequent visits\n    if (loading && initialLoad) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 bg-gray-200 rounded w-1/3 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-200 rounded w-1/2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: [\n                        ...Array(4)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card p-6 animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded w-1/2 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 bg-gray-200 rounded w-3/4 mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-3 bg-gray-200 rounded w-1/3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 253,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-slide-in\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold mb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400\",\n                                    children: [\n                                        \"Welcome \",\n                                        firstName,\n                                        \"! \\uD83D\\uDC4B\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-lg\",\n                                children: \"Here's what's happening with your LLM infrastructure today.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6 text-center mt-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-400 mb-4\",\n                                children: [\n                                    \"Error loading analytics data: \",\n                                    error\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: fetchAnalyticsData,\n                                className: \"btn-primary\",\n                                children: \"Retry\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 274,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 273,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full px-4 sm:px-6 lg:px-8 py-8 space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-slide-in\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold mb-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400\",\n                                children: [\n                                    \"Welcome \",\n                                    firstName,\n                                    \"! \\uD83D\\uDC4B\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-lg\",\n                            children: \"Here's what's happening with your LLM infrastructure today.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 303,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 animate-slide-in\",\n                    children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50 hover:border-gray-700/50 transition-all duration-200\",\n                            style: {\n                                animationDelay: \"\".concat(index * 100, \"ms\")\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400 mb-1\",\n                                                children: stat.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-3xl font-bold text-white mt-2\",\n                                                children: stat.value\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm mt-2 flex items-center \".concat(stat.changeType === 'positive' ? 'text-green-400' : stat.changeType === 'negative' ? 'text-red-400' : 'text-gray-500'),\n                                                children: [\n                                                    stat.changeType !== 'neutral' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1 \".concat(stat.changeType === 'negative' ? 'rotate-180' : '')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    stat.change\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-500\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 15\n                            }, this)\n                        }, stat.name, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 315,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6 animate-slide-in\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-white mb-4\",\n                                            children: \"Quick Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleAddNewModel,\n                                                    className: \"btn-primary w-full justify-center hover:scale-105 transition-transform duration-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        \"Add New Model\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleTestPlayground,\n                                                    className: \"btn-secondary-dark w-full justify-center hover:scale-105 transition-transform duration-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        \"Test in Playground\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleViewLogs,\n                                                    className: \"btn-secondary-dark w-full justify-center hover:scale-105 transition-transform duration-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        \"View Logs\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6 animate-slide-in\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-white mb-4\",\n                                            children: \"System Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: systemStatus.map((system)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded-full mr-3 \".concat(system.status === 'operational' ? 'bg-green-500' : system.status === 'degraded' ? 'bg-yellow-500' : 'bg-red-500')\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 384,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-300\",\n                                                                    children: system.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 388,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 383,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium \".concat(system.status === 'operational' ? 'text-green-400' : system.status === 'degraded' ? 'text-yellow-400' : 'text-red-400'),\n                                                                    children: system.status === 'operational' ? 'Operational' : system.status === 'degraded' ? 'Degraded' : 'Down'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 391,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                system.lastChecked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: system.lastChecked\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 399,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, system.name, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 17\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 349,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6 animate-slide-in\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold text-white\",\n                                                children: \"Recent Activity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: fetchRecentActivity,\n                                                className: \"text-orange-400 hover:text-orange-300 text-sm font-medium\",\n                                                children: \"Refresh\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            recentActivity.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-12 w-12 text-gray-500 mx-auto mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400\",\n                                                        children: \"No recent activity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 text-sm\",\n                                                        children: \"Activity will appear here as you use the API\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 17\n                                            }, this) : recentActivity.slice(-4).map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start p-4 rounded-lg bg-white/5 hover:bg-white/10 transition-colors duration-200 group overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 rounded-full mr-4 \".concat(activity.status === 'success' ? 'bg-green-500' : activity.status === 'warning' ? 'bg-yellow-500' : activity.status === 'error' ? 'bg-red-500' : 'bg-blue-500')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-white font-medium break-words\",\n                                                                    children: activity.action\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 436,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-400 text-sm break-words\",\n                                                                    children: [\n                                                                        activity.model,\n                                                                        \" • \",\n                                                                        activity.time\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 437,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                activity.details && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-500 text-xs mt-1 line-clamp-2 leading-relaxed\",\n                                                                    title: activity.details,\n                                                                    children: activity.details\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 439,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-400 group-hover:text-gray-300\",\n                                                            children: activity.status === 'error' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-5 w-5 text-red-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 446,\n                                                                columnNumber: 25\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, activity.id, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 19\n                                                }, this)),\n                                            recentActivity.length > 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 pt-4 border-t border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        // Navigate to logs page to see all activity\n                                                        window.location.href = '/logs';\n                                                    },\n                                                    className: \"text-sm text-orange-600 hover:text-orange-700 font-medium flex items-center justify-center w-full py-2 hover:bg-orange-50 rounded-lg transition-colors\",\n                                                    children: [\n                                                        \"View All Activity (\",\n                                                        recentActivity.length,\n                                                        \")\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 ml-1\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M9 5l7 7-7 7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 466,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 409,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 347,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 301,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 300,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"W8IW2nRDujSuRPyyGICkyuxiVCU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__.useSubscription\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});