"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/page",{

/***/ "(app-pages-browser)/./src/components/TierEnforcement/UpgradePrompt.tsx":
/*!**********************************************************!*\
  !*** ./src/components/TierEnforcement/UpgradePrompt.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UpgradePrompt: () => (/* binding */ UpgradePrompt)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_LockClosedIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,LockClosedIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/LockClosedIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_LockClosedIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,LockClosedIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_LockClosedIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,LockClosedIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _lib_stripe_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/stripe-client */ \"(app-pages-browser)/./src/lib/stripe-client.ts\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ UpgradePrompt auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst featureDisplayNames = {\n    custom_roles: 'Custom Roles',\n    knowledge_base: 'Knowledge Base',\n    advanced_routing: 'Advanced Routing',\n    prompt_engineering: 'Prompt Engineering',\n    semantic_caching: 'Semantic Caching',\n    configurations: 'API Configurations'\n};\nconst getMinimumTierForFeature = (feature)=>{\n    const tiers = [\n        'starter',\n        'professional',\n        'enterprise'\n    ];\n    for (const tier of tiers){\n        const config = _lib_stripe_client__WEBPACK_IMPORTED_MODULE_2__.TIER_CONFIGS[tier];\n        switch(feature){\n            case 'custom_roles':\n                if (config.limits.canUseCustomRoles) return tier;\n                break;\n            case 'knowledge_base':\n                if (config.limits.canUseKnowledgeBase) return tier;\n                break;\n            case 'advanced_routing':\n                if (config.limits.canUseAdvancedRouting) return tier;\n                break;\n            case 'prompt_engineering':\n                if (config.limits.canUsePromptEngineering) return tier;\n                break;\n            case 'semantic_caching':\n                if (config.limits.canUseSemanticCaching) return tier;\n                break;\n            case 'configurations':\n                // For configurations, find the tier that has more configurations than free tier\n                if (config.limits.configurations > _lib_stripe_client__WEBPACK_IMPORTED_MODULE_2__.TIER_CONFIGS.free.limits.configurations) return tier;\n                break;\n        }\n    }\n    return 'starter';\n};\nfunction UpgradePrompt(param) {\n    let { feature, currentTier, customMessage, size = 'md', variant = 'card', theme = 'light' } = param;\n    _s();\n    const { createCheckoutSession } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__.useSubscription)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const minimumTier = getMinimumTierForFeature(feature);\n    const minimumTierConfig = _lib_stripe_client__WEBPACK_IMPORTED_MODULE_2__.TIER_CONFIGS[minimumTier];\n    const featureName = featureDisplayNames[feature];\n    const handleUpgrade = async ()=>{\n        try {\n            if (minimumTier === 'starter') {\n                await createCheckoutSession('starter');\n            } else if (minimumTier === 'professional') {\n                await createCheckoutSession('professional');\n            } else {\n                router.push('/pricing');\n            }\n        } catch (error) {\n            console.error('Error creating checkout session:', error);\n            router.push('/pricing');\n        }\n    };\n    const sizeClasses = {\n        sm: 'p-4 text-sm',\n        md: 'p-6 text-base',\n        lg: 'p-8 text-lg'\n    };\n    const variantClasses = {\n        card: 'bg-gradient-to-br from-orange-50 to-orange-100 border border-orange-200 rounded-xl shadow-sm',\n        banner: 'bg-orange-100 border-l-4 border-orange-500 rounded-r-lg',\n        inline: 'bg-orange-50 border border-orange-200 rounded-lg'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        className: \"\".concat(variantClasses[variant], \" \").concat(sizeClasses[size]),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_LockClosedIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                            children: [\n                                featureName,\n                                \" - Premium Feature\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-700 mb-4\",\n                            children: customMessage || \"\".concat(featureName, \" is available starting with the \").concat(minimumTierConfig.name, \" plan. \\n               Upgrade to unlock this powerful feature and enhance your RouKey experience.\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleUpgrade,\n                                    className: \"inline-flex items-center px-4 py-2 bg-orange-600 text-white text-sm font-medium rounded-lg hover:bg-orange-700 transition-colors duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_LockClosedIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Upgrade to \",\n                                        minimumTierConfig.name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/pricing'),\n                                    className: \"inline-flex items-center px-4 py-2 text-orange-600 text-sm font-medium hover:text-orange-700 transition-colors duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_LockClosedIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"View All Plans\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n            lineNumber: 105,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, this);\n}\n_s(UpgradePrompt, \"mQL/U3l0hNJhYvttzby62V49684=\", false, function() {\n    return [\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__.useSubscription,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = UpgradePrompt;\nvar _c;\n$RefreshReg$(_c, \"UpgradePrompt\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TierEnforcement/UpgradePrompt.tsx\n"));

/***/ })

});