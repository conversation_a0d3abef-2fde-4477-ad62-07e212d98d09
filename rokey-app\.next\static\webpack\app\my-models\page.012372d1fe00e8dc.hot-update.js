"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/page",{

/***/ "(app-pages-browser)/./src/components/TierEnforcement/UpgradePrompt.tsx":
/*!**********************************************************!*\
  !*** ./src/components/TierEnforcement/UpgradePrompt.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UpgradePrompt: () => (/* binding */ UpgradePrompt)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_LockClosedIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,LockClosedIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/LockClosedIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_LockClosedIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,LockClosedIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_LockClosedIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,LockClosedIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _lib_stripe_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/stripe-client */ \"(app-pages-browser)/./src/lib/stripe-client.ts\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ UpgradePrompt auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst featureDisplayNames = {\n    custom_roles: 'Custom Roles',\n    knowledge_base: 'Knowledge Base',\n    advanced_routing: 'Advanced Routing',\n    prompt_engineering: 'Prompt Engineering',\n    semantic_caching: 'Semantic Caching',\n    configurations: 'API Configurations'\n};\nconst getMinimumTierForFeature = (feature)=>{\n    const tiers = [\n        'starter',\n        'professional',\n        'enterprise'\n    ];\n    for (const tier of tiers){\n        const config = _lib_stripe_client__WEBPACK_IMPORTED_MODULE_2__.TIER_CONFIGS[tier];\n        switch(feature){\n            case 'custom_roles':\n                if (config.limits.canUseCustomRoles) return tier;\n                break;\n            case 'knowledge_base':\n                if (config.limits.canUseKnowledgeBase) return tier;\n                break;\n            case 'advanced_routing':\n                if (config.limits.canUseAdvancedRouting) return tier;\n                break;\n            case 'prompt_engineering':\n                if (config.limits.canUsePromptEngineering) return tier;\n                break;\n            case 'semantic_caching':\n                if (config.limits.canUseSemanticCaching) return tier;\n                break;\n            case 'configurations':\n                // For configurations, find the tier that has more configurations than free tier\n                if (config.limits.configurations > _lib_stripe_client__WEBPACK_IMPORTED_MODULE_2__.TIER_CONFIGS.free.limits.configurations) return tier;\n                break;\n        }\n    }\n    return 'starter';\n};\nfunction UpgradePrompt(param) {\n    let { feature, currentTier, customMessage, size = 'md', variant = 'card', theme = 'light' } = param;\n    _s();\n    const { createCheckoutSession } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__.useSubscription)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const minimumTier = getMinimumTierForFeature(feature);\n    const minimumTierConfig = _lib_stripe_client__WEBPACK_IMPORTED_MODULE_2__.TIER_CONFIGS[minimumTier];\n    const featureName = featureDisplayNames[feature];\n    const handleUpgrade = async ()=>{\n        try {\n            if (minimumTier === 'starter') {\n                await createCheckoutSession('starter');\n            } else if (minimumTier === 'professional') {\n                await createCheckoutSession('professional');\n            } else {\n                router.push('/pricing');\n            }\n        } catch (error) {\n            console.error('Error creating checkout session:', error);\n            router.push('/pricing');\n        }\n    };\n    const sizeClasses = {\n        sm: 'p-4 text-sm',\n        md: 'p-6 text-base',\n        lg: 'p-8 text-lg'\n    };\n    const variantClasses = {\n        card: theme === 'dark' ? 'bg-gradient-to-br from-orange-900/20 to-orange-800/20 border border-orange-500/30 rounded-xl shadow-sm' : 'bg-gradient-to-br from-orange-50 to-orange-100 border border-orange-200 rounded-xl shadow-sm',\n        banner: theme === 'dark' ? 'bg-orange-900/20 border-l-4 border-orange-500 rounded-r-lg' : 'bg-orange-100 border-l-4 border-orange-500 rounded-r-lg',\n        inline: theme === 'dark' ? 'bg-orange-900/20 border border-orange-500/30 rounded-lg' : 'bg-orange-50 border border-orange-200 rounded-lg'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        className: \"\".concat(variantClasses[variant], \" \").concat(sizeClasses[size]),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_LockClosedIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-2 \".concat(theme === 'dark' ? 'text-white' : 'text-gray-900'),\n                            children: [\n                                featureName,\n                                \" - Premium Feature\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-4 \".concat(theme === 'dark' ? 'text-gray-300' : 'text-gray-700'),\n                            children: customMessage || \"\".concat(featureName, \" is available starting with the \").concat(minimumTierConfig.name, \" plan.\\n               Upgrade to unlock this powerful feature and enhance your RouKey experience.\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleUpgrade,\n                                    className: \"inline-flex items-center px-4 py-2 bg-orange-600 text-white text-sm font-medium rounded-lg hover:bg-orange-700 transition-colors duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_LockClosedIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Upgrade to \",\n                                        minimumTierConfig.name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/pricing'),\n                                    className: \"inline-flex items-center px-4 py-2 text-orange-600 text-sm font-medium hover:text-orange-700 transition-colors duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_LockClosedIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"View All Plans\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n            lineNumber: 111,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, this);\n}\n_s(UpgradePrompt, \"mQL/U3l0hNJhYvttzby62V49684=\", false, function() {\n    return [\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__.useSubscription,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = UpgradePrompt;\nvar _c;\n$RefreshReg$(_c, \"UpgradePrompt\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TierEnforcement/UpgradePrompt.tsx\n"));

/***/ })

});