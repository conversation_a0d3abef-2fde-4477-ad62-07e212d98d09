"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/[configId]/page",{

/***/ "(app-pages-browser)/./src/app/my-models/[configId]/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/my-models/[configId]/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ConfigDetailsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _config_models__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/config/models */ \"(app-pages-browser)/./src/config/models.ts\");\n/* harmony import */ var _config_roles__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/roles */ \"(app-pages-browser)/./src/config/roles.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowLeftIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudArrowDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/InformationCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var react_tooltip__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-tooltip */ \"(app-pages-browser)/./node_modules/react-tooltip/dist/react-tooltip.min.mjs\");\n/* harmony import */ var _components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/ConfirmationModal */ \"(app-pages-browser)/./src/components/ui/ConfirmationModal.tsx\");\n/* harmony import */ var _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useConfirmation */ \"(app-pages-browser)/./src/hooks/useConfirmation.ts\");\n/* harmony import */ var _hooks_useManageKeysPrefetch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useManageKeysPrefetch */ \"(app-pages-browser)/./src/hooks/useManageKeysPrefetch.ts\");\n/* harmony import */ var _components_ManageKeysLoadingSkeleton__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ManageKeysLoadingSkeleton */ \"(app-pages-browser)/./src/components/ManageKeysLoadingSkeleton.tsx\");\n/* harmony import */ var _hooks_useRoutingSetupPrefetch__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/useRoutingSetupPrefetch */ \"(app-pages-browser)/./src/hooks/useRoutingSetupPrefetch.ts\");\n/* harmony import */ var _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/contexts/NavigationContext */ \"(app-pages-browser)/./src/contexts/NavigationContext.tsx\");\n/* harmony import */ var _components_UserApiKeys_ApiKeyManager__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/UserApiKeys/ApiKeyManager */ \"(app-pages-browser)/./src/components/UserApiKeys/ApiKeyManager.tsx\");\n/* harmony import */ var _components_TierEnforcement__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/TierEnforcement */ \"(app-pages-browser)/./src/components/TierEnforcement/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n // For accessing route params\n // Ensure path is correct\n\n\n\n\n\n\n\n\n\n\n\n// Updated: PROVIDER_OPTIONS uses p.id (slug) for value and p.name for label\nconst PROVIDER_OPTIONS = _config_models__WEBPACK_IMPORTED_MODULE_3__.llmProviders.map(_c = (p)=>({\n        value: p.id,\n        label: p.name\n    }));\n_c1 = PROVIDER_OPTIONS;\nfunction ConfigDetailsPage() {\n    var _PROVIDER_OPTIONS_, _llmProviders_find;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const configId = params.configId;\n    // Confirmation modal hook\n    const confirmation = (0,_hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_7__.useConfirmation)();\n    // Navigation hook with safe context\n    const navigationContext = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_11__.useNavigationSafe)();\n    const navigateOptimistically = (navigationContext === null || navigationContext === void 0 ? void 0 : navigationContext.navigateOptimistically) || ((href)=>{\n        window.location.href = href;\n    });\n    // Prefetch hooks\n    const { getCachedData, isCached, clearCache } = (0,_hooks_useManageKeysPrefetch__WEBPACK_IMPORTED_MODULE_8__.useManageKeysPrefetch)();\n    const { createHoverPrefetch: createRoutingHoverPrefetch } = (0,_hooks_useRoutingSetupPrefetch__WEBPACK_IMPORTED_MODULE_10__.useRoutingSetupPrefetch)();\n    const [configDetails, setConfigDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoadingConfig, setIsLoadingConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showOptimisticLoading, setShowOptimisticLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Provider state now stores the slug (p.id)\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(((_PROVIDER_OPTIONS_ = PROVIDER_OPTIONS[0]) === null || _PROVIDER_OPTIONS_ === void 0 ? void 0 : _PROVIDER_OPTIONS_.value) || 'openai'); // Stores slug\n    const [predefinedModelId, setPredefinedModelId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [apiKeyRaw, setApiKeyRaw] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [label, setLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [temperature, setTemperature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.0);\n    const [isSavingKey, setIsSavingKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State for dynamic model fetching\n    const [fetchedProviderModels, setFetchedProviderModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isFetchingProviderModels, setIsFetchingProviderModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [fetchProviderModelsError, setFetchProviderModelsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [savedKeysWithRoles, setSavedKeysWithRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingKeysAndRoles, setIsLoadingKeysAndRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDeletingKey, setIsDeletingKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [defaultGeneralChatKeyId, setDefaultGeneralChatKeyId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingRolesApiKey, setEditingRolesApiKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State for editing API keys\n    const [editingApiKey, setEditingApiKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editTemperature, setEditTemperature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.0);\n    const [editPredefinedModelId, setEditPredefinedModelId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSavingEdit, setIsSavingEdit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // State for User-Defined Custom Roles\n    const [userCustomRoles, setUserCustomRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingUserCustomRoles, setIsLoadingUserCustomRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userCustomRolesError, setUserCustomRolesError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCreateCustomRoleForm, setShowCreateCustomRoleForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newCustomRoleId, setNewCustomRoleId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newCustomRoleName, setNewCustomRoleName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newCustomRoleDescription, setNewCustomRoleDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSavingCustomRole, setIsSavingCustomRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createCustomRoleError, setCreateCustomRoleError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deletingCustomRoleId, setDeletingCustomRoleId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // stores the DB ID (UUID) of the custom role\n    // State for tab management\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('provider-keys');\n    // Fetch config details with optimistic loading\n    const fetchConfigDetails = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConfigDetailsPage.useCallback[fetchConfigDetails]\": async ()=>{\n            if (!configId) return;\n            // Check for cached data first\n            const cachedData = getCachedData(configId);\n            if (cachedData && cachedData.configDetails) {\n                console.log(\"⚡ [MANAGE KEYS] Using cached config data for: \".concat(configId));\n                setConfigDetails(cachedData.configDetails);\n                setIsLoadingConfig(false);\n                return;\n            }\n            // Show optimistic loading for first-time visits\n            if (!isCached(configId)) {\n                setShowOptimisticLoading(true);\n            }\n            setIsLoadingConfig(true);\n            setError(null);\n            try {\n                const res = await fetch(\"/api/custom-configs\");\n                if (!res.ok) {\n                    const errData = await res.json();\n                    throw new Error(errData.error || 'Failed to fetch configurations list');\n                }\n                const allConfigs = await res.json();\n                const currentConfig = allConfigs.find({\n                    \"ConfigDetailsPage.useCallback[fetchConfigDetails].currentConfig\": (c)=>c.id === configId\n                }[\"ConfigDetailsPage.useCallback[fetchConfigDetails].currentConfig\"]);\n                if (!currentConfig) throw new Error('Configuration not found in the list.');\n                setConfigDetails(currentConfig);\n            } catch (err) {\n                setError(\"Error loading model configuration: \".concat(err.message));\n                setConfigDetails(null);\n            } finally{\n                setIsLoadingConfig(false);\n                setShowOptimisticLoading(false);\n            }\n        }\n    }[\"ConfigDetailsPage.useCallback[fetchConfigDetails]\"], [\n        configId,\n        getCachedData,\n        isCached\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            fetchConfigDetails();\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        fetchConfigDetails\n    ]);\n    // New: Function to fetch all models from the database with caching\n    const fetchModelsFromDatabase = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConfigDetailsPage.useCallback[fetchModelsFromDatabase]\": async ()=>{\n            // Check for cached data first\n            const cachedData = getCachedData(configId);\n            if (cachedData && cachedData.models) {\n                console.log(\"⚡ [MANAGE KEYS] Using cached models data for: \".concat(configId));\n                setFetchedProviderModels(cachedData.models);\n                setIsFetchingProviderModels(false);\n                return;\n            }\n            setIsFetchingProviderModels(true);\n            setFetchProviderModelsError(null);\n            setFetchedProviderModels(null);\n            try {\n                // The new API doesn't need a specific provider or API key in the body to list models\n                const response = await fetch('/api/providers/list-models', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({})\n                });\n                const data = await response.json();\n                if (!response.ok) {\n                    throw new Error(data.error || 'Failed to fetch models from database.');\n                }\n                if (data.models) {\n                    setFetchedProviderModels(data.models);\n                } else {\n                    setFetchedProviderModels([]);\n                }\n            } catch (err) {\n                setFetchProviderModelsError(\"Error fetching models: \".concat(err.message));\n                setFetchedProviderModels([]); // Set to empty array on error to prevent blocking UI\n            } finally{\n                setIsFetchingProviderModels(false);\n            }\n        }\n    }[\"ConfigDetailsPage.useCallback[fetchModelsFromDatabase]\"], [\n        configId,\n        getCachedData\n    ]);\n    // New: Fetch all models from DB when configId is available (i.e., page is ready)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            if (configId) {\n                fetchModelsFromDatabase();\n            }\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        configId,\n        fetchModelsFromDatabase\n    ]);\n    // Updated: Function to fetch all global custom roles for the authenticated user with caching\n    const fetchUserCustomRoles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConfigDetailsPage.useCallback[fetchUserCustomRoles]\": async ()=>{\n            // Check for cached data first\n            const cachedData = getCachedData(configId);\n            if (cachedData && cachedData.userCustomRoles) {\n                console.log(\"⚡ [MANAGE KEYS] Using cached custom roles data for: \".concat(configId));\n                setUserCustomRoles(cachedData.userCustomRoles);\n                setIsLoadingUserCustomRoles(false);\n                return;\n            }\n            setIsLoadingUserCustomRoles(true);\n            setUserCustomRolesError(null);\n            try {\n                const response = await fetch(\"/api/user/custom-roles\"); // New global endpoint\n                if (!response.ok) {\n                    let errorData;\n                    try {\n                        errorData = await response.json(); // Attempt to parse error as JSON\n                    } catch (e) {\n                        // If error response is not JSON, use text or a generic error\n                        const errorText = await response.text().catch({\n                            \"ConfigDetailsPage.useCallback[fetchUserCustomRoles]\": ()=>\"HTTP error \".concat(response.status)\n                        }[\"ConfigDetailsPage.useCallback[fetchUserCustomRoles]\"]);\n                        errorData = {\n                            error: errorText\n                        };\n                    }\n                    const errorMessage = errorData.error || (errorData.issues ? JSON.stringify(errorData.issues) : \"Failed to fetch custom roles (status: \".concat(response.status, \")\"));\n                    if (response.status === 401) {\n                        setUserCustomRolesError(errorMessage);\n                    } else {\n                        throw new Error(errorMessage); // Throw for other errors to be caught by the main catch\n                    }\n                    setUserCustomRoles([]); // Clear roles if there was an error handled here\n                } else {\n                    // Only call .json() here if response.ok and body hasn't been read\n                    const data = await response.json();\n                    setUserCustomRoles(data);\n                // setUserCustomRolesError(null); // Clearing error on success is good, but already done at the start of try\n                }\n            } catch (err) {\n                // This catch handles network errors from fetch() or errors thrown from !response.ok block\n                setUserCustomRolesError(err.message);\n                setUserCustomRoles([]); // Clear roles on error\n            } finally{\n                setIsLoadingUserCustomRoles(false);\n            }\n        }\n    }[\"ConfigDetailsPage.useCallback[fetchUserCustomRoles]\"], []);\n    // Fetch API keys and their roles for this config with optimistic loading\n    const fetchKeysAndRolesForConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\": async ()=>{\n            if (!configId || !userCustomRoles) return; // Also wait for userCustomRoles to be available\n            // Check for cached data first\n            const cachedData = getCachedData(configId);\n            if (cachedData && cachedData.apiKeys && cachedData.defaultChatKeyId !== undefined) {\n                console.log(\"⚡ [MANAGE KEYS] Using cached keys data for: \".concat(configId));\n                // Process cached keys with roles (same logic as below)\n                const keysWithRolesPromises = cachedData.apiKeys.map({\n                    \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\": async (key)=>{\n                        const rolesResponse = await fetch(\"/api/keys/\".concat(key.id, \"/roles\"));\n                        let assigned_roles = [];\n                        if (rolesResponse.ok) {\n                            const roleAssignments = await rolesResponse.json();\n                            assigned_roles = roleAssignments.map({\n                                \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\": (ra)=>{\n                                    const predefinedRole = (0,_config_roles__WEBPACK_IMPORTED_MODULE_4__.getRoleById)(ra.role_name);\n                                    if (predefinedRole) return predefinedRole;\n                                    const customRole = userCustomRoles.find({\n                                        \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises.customRole\": (cr)=>cr.role_id === ra.role_name\n                                    }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises.customRole\"]);\n                                    if (customRole) {\n                                        return {\n                                            id: customRole.role_id,\n                                            name: customRole.name,\n                                            description: customRole.description || undefined\n                                        };\n                                    }\n                                    return null;\n                                }\n                            }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\"]).filter(Boolean);\n                        }\n                        return {\n                            ...key,\n                            assigned_roles,\n                            is_default_general_chat_model: cachedData.defaultChatKeyId === key.id\n                        };\n                    }\n                }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\"]);\n                const resolvedKeysWithRoles = await Promise.all(keysWithRolesPromises);\n                setSavedKeysWithRoles(resolvedKeysWithRoles);\n                setDefaultGeneralChatKeyId(cachedData.defaultChatKeyId);\n                setIsLoadingKeysAndRoles(false);\n                return;\n            }\n            setIsLoadingKeysAndRoles(true);\n            // Preserve config loading errors, clear others\n            setError({\n                \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\": (prev)=>prev && prev.startsWith('Error loading model configuration:') ? prev : null\n            }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\"]);\n            setSuccessMessage(null);\n            try {\n                // Fetch all keys for the config\n                const keysResponse = await fetch(\"/api/keys?custom_config_id=\".concat(configId));\n                if (!keysResponse.ok) {\n                    const errorData = await keysResponse.json();\n                    throw new Error(errorData.error || 'Failed to fetch API keys');\n                }\n                const keys = await keysResponse.json();\n                // Fetch default general chat key\n                const defaultKeyResponse = await fetch(\"/api/custom-configs/\".concat(configId, \"/default-chat-key\"));\n                if (!defaultKeyResponse.ok) {\n                    console.warn('Failed to fetch default chat key info');\n                }\n                const defaultKeyData = defaultKeyResponse.status === 200 ? await defaultKeyResponse.json() : null;\n                setDefaultGeneralChatKeyId((defaultKeyData === null || defaultKeyData === void 0 ? void 0 : defaultKeyData.id) || null);\n                // For each key, fetch its assigned roles\n                const keysWithRolesPromises = keys.map({\n                    \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\": async (key)=>{\n                        const rolesResponse = await fetch(\"/api/keys/\".concat(key.id, \"/roles\"));\n                        let assigned_roles = [];\n                        if (rolesResponse.ok) {\n                            const roleAssignments = await rolesResponse.json();\n                            assigned_roles = roleAssignments.map({\n                                \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\": (ra)=>{\n                                    // 1. Check predefined roles\n                                    const predefinedRole = (0,_config_roles__WEBPACK_IMPORTED_MODULE_4__.getRoleById)(ra.role_name);\n                                    if (predefinedRole) return predefinedRole;\n                                    // 2. Check current user's global custom roles\n                                    const customRole = userCustomRoles.find({\n                                        \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises.customRole\": (cr)=>cr.role_id === ra.role_name\n                                    }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises.customRole\"]);\n                                    if (customRole) {\n                                        return {\n                                            id: customRole.role_id,\n                                            name: customRole.name,\n                                            description: customRole.description || undefined\n                                        };\n                                    }\n                                    // 3. If not found in either, it's a lingering assignment to a deleted/invalid role, so filter it out\n                                    return null;\n                                }\n                            }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\"]).filter(Boolean); // filter(Boolean) removes null entries\n                        }\n                        return {\n                            ...key,\n                            assigned_roles,\n                            is_default_general_chat_model: (defaultKeyData === null || defaultKeyData === void 0 ? void 0 : defaultKeyData.id) === key.id\n                        };\n                    }\n                }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\"]);\n                const resolvedKeysWithRoles = await Promise.all(keysWithRolesPromises);\n                setSavedKeysWithRoles(resolvedKeysWithRoles);\n            } catch (err) {\n                setError({\n                    \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\": (prev)=>prev ? \"\".concat(prev, \"; \").concat(err.message) : err.message\n                }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\"]); // Append if there was a config load error\n            } finally{\n                setIsLoadingKeysAndRoles(false);\n            }\n        }\n    }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\"], [\n        configId,\n        userCustomRoles\n    ]); // Added userCustomRoles to dependency array\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            if (configDetails) {\n                fetchUserCustomRoles(); // Call to fetch custom roles\n            }\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        configDetails,\n        fetchUserCustomRoles\n    ]); // Only depends on configDetails and the stable fetchUserCustomRoles\n    // New useEffect to fetch keys and roles when configDetails AND userCustomRoles (state) are ready\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            // Ensure userCustomRoles is not in its initial undefined/null state from useState([])\n            // and actually contains data (or an empty array confirming fetch completion)\n            if (configDetails && userCustomRoles) {\n                fetchKeysAndRolesForConfig();\n            }\n        // This effect runs if configDetails changes, userCustomRoles (state) changes,\n        // or fetchKeysAndRolesForConfig function identity changes (which happens if userCustomRoles state changes).\n        // This is the desired behavior: re-fetch keys/roles if custom roles change.\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        configDetails,\n        userCustomRoles,\n        fetchKeysAndRolesForConfig\n    ]);\n    // Updated: Memoize model options based on selected provider and fetched models\n    const modelOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ConfigDetailsPage.useMemo[modelOptions]\": ()=>{\n            if (fetchedProviderModels) {\n                const currentProviderDetails = _config_models__WEBPACK_IMPORTED_MODULE_3__.llmProviders.find({\n                    \"ConfigDetailsPage.useMemo[modelOptions].currentProviderDetails\": (p)=>p.id === provider\n                }[\"ConfigDetailsPage.useMemo[modelOptions].currentProviderDetails\"]);\n                if (!currentProviderDetails) return [];\n                // If the selected provider is \"OpenRouter\", show all fetched models\n                // as an OpenRouter key can access any of them.\n                if (currentProviderDetails.id === \"openrouter\") {\n                    return fetchedProviderModels.map({\n                        \"ConfigDetailsPage.useMemo[modelOptions]\": (m)=>({\n                                value: m.id,\n                                label: m.display_name || m.name,\n                                provider_id: m.provider_id\n                            })\n                    }[\"ConfigDetailsPage.useMemo[modelOptions]\"]).sort({\n                        \"ConfigDetailsPage.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"ConfigDetailsPage.useMemo[modelOptions]\"]);\n                }\n                // Custom logic for DeepSeek\n                if (currentProviderDetails.id === \"deepseek\") {\n                    console.log('[DeepSeek Debug] Provider is DeepSeek. Fetched models:', JSON.stringify(fetchedProviderModels));\n                    const deepseekOptions = [];\n                    const deepseekChatModel = fetchedProviderModels.find({\n                        \"ConfigDetailsPage.useMemo[modelOptions].deepseekChatModel\": (model)=>model.id === \"deepseek-chat\" && model.provider_id === \"deepseek\"\n                    }[\"ConfigDetailsPage.useMemo[modelOptions].deepseekChatModel\"]);\n                    console.log('[DeepSeek Debug] Found deepseek-chat model:', JSON.stringify(deepseekChatModel));\n                    if (deepseekChatModel) {\n                        deepseekOptions.push({\n                            value: \"deepseek-chat\",\n                            label: \"Deepseek V3\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    const deepseekReasonerModel = fetchedProviderModels.find({\n                        \"ConfigDetailsPage.useMemo[modelOptions].deepseekReasonerModel\": (model)=>model.id === \"deepseek-reasoner\" && model.provider_id === \"deepseek\"\n                    }[\"ConfigDetailsPage.useMemo[modelOptions].deepseekReasonerModel\"]);\n                    console.log('[DeepSeek Debug] Found deepseek-reasoner model:', JSON.stringify(deepseekReasonerModel));\n                    if (deepseekReasonerModel) {\n                        deepseekOptions.push({\n                            value: \"deepseek-reasoner\",\n                            label: \"DeepSeek R1-0528\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    // If for some reason the specific models are not found in fetchedProviderModels,\n                    // it's better to return an empty array or a message than all DeepSeek models unfiltered.\n                    // Or, as a fallback, show all models for DeepSeek if the specific ones aren't present.\n                    // For now, strictly showing only these two if found.\n                    return deepseekOptions.sort({\n                        \"ConfigDetailsPage.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"ConfigDetailsPage.useMemo[modelOptions]\"]);\n                }\n                // For other providers, filter by their specific provider_id\n                return fetchedProviderModels.filter({\n                    \"ConfigDetailsPage.useMemo[modelOptions]\": (model)=>model.provider_id === currentProviderDetails.id\n                }[\"ConfigDetailsPage.useMemo[modelOptions]\"]).map({\n                    \"ConfigDetailsPage.useMemo[modelOptions]\": (m)=>({\n                            value: m.id,\n                            label: m.display_name || m.name,\n                            provider_id: m.provider_id\n                        })\n                }[\"ConfigDetailsPage.useMemo[modelOptions]\"]).sort({\n                    \"ConfigDetailsPage.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                }[\"ConfigDetailsPage.useMemo[modelOptions]\"]);\n            }\n            return []; // Return empty array if models haven't been fetched or if fetch failed.\n        }\n    }[\"ConfigDetailsPage.useMemo[modelOptions]\"], [\n        fetchedProviderModels,\n        provider\n    ]);\n    // Model options for edit modal - filtered by the current key's provider\n    const editModelOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ConfigDetailsPage.useMemo[editModelOptions]\": ()=>{\n            if (fetchedProviderModels && editingApiKey) {\n                const currentProviderDetails = _config_models__WEBPACK_IMPORTED_MODULE_3__.llmProviders.find({\n                    \"ConfigDetailsPage.useMemo[editModelOptions].currentProviderDetails\": (p)=>p.id === editingApiKey.provider\n                }[\"ConfigDetailsPage.useMemo[editModelOptions].currentProviderDetails\"]);\n                if (!currentProviderDetails) return [];\n                // If the provider is \"OpenRouter\", show all fetched models\n                if (currentProviderDetails.id === \"openrouter\") {\n                    return fetchedProviderModels.map({\n                        \"ConfigDetailsPage.useMemo[editModelOptions]\": (m)=>({\n                                value: m.id,\n                                label: m.display_name || m.name,\n                                provider_id: m.provider_id\n                            })\n                    }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]).sort({\n                        \"ConfigDetailsPage.useMemo[editModelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]);\n                }\n                // Custom logic for DeepSeek\n                if (currentProviderDetails.id === \"deepseek\") {\n                    const deepseekOptions = [];\n                    const deepseekChatModel = fetchedProviderModels.find({\n                        \"ConfigDetailsPage.useMemo[editModelOptions].deepseekChatModel\": (model)=>model.id === \"deepseek-chat\" && model.provider_id === \"deepseek\"\n                    }[\"ConfigDetailsPage.useMemo[editModelOptions].deepseekChatModel\"]);\n                    if (deepseekChatModel) {\n                        deepseekOptions.push({\n                            value: \"deepseek-chat\",\n                            label: \"Deepseek V3\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    const deepseekReasonerModel = fetchedProviderModels.find({\n                        \"ConfigDetailsPage.useMemo[editModelOptions].deepseekReasonerModel\": (model)=>model.id === \"deepseek-reasoner\" && model.provider_id === \"deepseek\"\n                    }[\"ConfigDetailsPage.useMemo[editModelOptions].deepseekReasonerModel\"]);\n                    if (deepseekReasonerModel) {\n                        deepseekOptions.push({\n                            value: \"deepseek-reasoner\",\n                            label: \"DeepSeek R1-0528\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    return deepseekOptions.sort({\n                        \"ConfigDetailsPage.useMemo[editModelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]);\n                }\n                // For other providers, filter by their specific provider_id\n                return fetchedProviderModels.filter({\n                    \"ConfigDetailsPage.useMemo[editModelOptions]\": (model)=>model.provider_id === currentProviderDetails.id\n                }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]).map({\n                    \"ConfigDetailsPage.useMemo[editModelOptions]\": (m)=>({\n                            value: m.id,\n                            label: m.display_name || m.name,\n                            provider_id: m.provider_id\n                        })\n                }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]).sort({\n                    \"ConfigDetailsPage.useMemo[editModelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]);\n            }\n            return [];\n        }\n    }[\"ConfigDetailsPage.useMemo[editModelOptions]\"], [\n        fetchedProviderModels,\n        editingApiKey\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            // Auto-select the first model from the dynamic modelOptions when provider changes or models load\n            if (modelOptions.length > 0) {\n                setPredefinedModelId(modelOptions[0].value);\n            } else {\n                setPredefinedModelId(''); // Clear if no models for provider\n            }\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        modelOptions,\n        provider\n    ]); // Now depends on modelOptions, which depends on fetchedProviderModels and provider\n    // Fetch models based on the provider's slug (p.id)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            if (provider) {\n                // Logic to fetch models for the selected provider slug might need adjustment\n                // if it was previously relying on the provider display name.\n                // Assuming fetchProviderModels is adapted or already uses slugs.\n                fetchModelsFromDatabase();\n            }\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        provider,\n        fetchModelsFromDatabase\n    ]);\n    const handleSaveKey = async (e)=>{\n        e.preventDefault();\n        if (!configId) {\n            setError('Configuration ID is missing.');\n            return;\n        }\n        // Frontend validation: Check for duplicate models\n        const isDuplicateModel = savedKeysWithRoles.some((key)=>key.predefined_model_id === predefinedModelId);\n        if (isDuplicateModel) {\n            setError('This model is already configured in this setup. Each model can only be used once per configuration, but you can use the same API key with different models.');\n            return;\n        }\n        setIsSavingKey(true);\n        setError(null);\n        setSuccessMessage(null);\n        // provider state variable already holds the slug\n        const newKeyData = {\n            custom_api_config_id: configId,\n            provider,\n            predefined_model_id: predefinedModelId,\n            api_key_raw: apiKeyRaw,\n            label,\n            temperature\n        };\n        // Store previous state for rollback on error\n        const previousKeysState = [\n            ...savedKeysWithRoles\n        ];\n        try {\n            var _PROVIDER_OPTIONS_;\n            const response = await fetch('/api/keys', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(newKeyData)\n            });\n            const result = await response.json();\n            if (!response.ok) throw new Error(result.details || result.error || 'Failed to save API key');\n            // Create optimistic key object with the returned data\n            const newKey = {\n                id: result.id,\n                custom_api_config_id: configId,\n                provider,\n                predefined_model_id: predefinedModelId,\n                label,\n                temperature,\n                status: 'active',\n                created_at: new Date().toISOString(),\n                last_used_at: null,\n                is_default_general_chat_model: false,\n                assigned_roles: []\n            };\n            // Optimistically add the new key to the list\n            setSavedKeysWithRoles((prevKeys)=>[\n                    ...prevKeys,\n                    newKey\n                ]);\n            // Clear cache to ensure fresh data on next fetch\n            clearCache(configId);\n            setSuccessMessage('API key \"'.concat(label, '\" saved successfully!'));\n            setProvider(((_PROVIDER_OPTIONS_ = PROVIDER_OPTIONS[0]) === null || _PROVIDER_OPTIONS_ === void 0 ? void 0 : _PROVIDER_OPTIONS_.value) || 'openai');\n            setApiKeyRaw('');\n            setLabel('');\n            setTemperature(1.0);\n            // Reset model selection to first available option\n            if (modelOptions.length > 0) {\n                setPredefinedModelId(modelOptions[0].value);\n            }\n        } catch (err) {\n            // Revert UI on error\n            setSavedKeysWithRoles(previousKeysState);\n            setError(\"Save Key Error: \".concat(err.message));\n        } finally{\n            setIsSavingKey(false);\n        }\n    };\n    const handleEditKey = (key)=>{\n        setEditingApiKey(key);\n        setEditTemperature(key.temperature || 1.0);\n        setEditPredefinedModelId(key.predefined_model_id);\n    };\n    const handleSaveEdit = async ()=>{\n        if (!editingApiKey) return;\n        // Frontend validation: Check for duplicate models (excluding the current key being edited)\n        const isDuplicateModel = savedKeysWithRoles.some((key)=>key.id !== editingApiKey.id && key.predefined_model_id === editPredefinedModelId);\n        if (isDuplicateModel) {\n            setError('This model is already configured in this setup. Each model can only be used once per configuration.');\n            return;\n        }\n        setIsSavingEdit(true);\n        setError(null);\n        setSuccessMessage(null);\n        // Store previous state for rollback on error\n        const previousKeysState = [\n            ...savedKeysWithRoles\n        ];\n        // Optimistic UI update\n        setSavedKeysWithRoles((prevKeys)=>prevKeys.map((key)=>{\n                if (key.id === editingApiKey.id) {\n                    return {\n                        ...key,\n                        temperature: editTemperature,\n                        predefined_model_id: editPredefinedModelId\n                    };\n                }\n                return key;\n            }));\n        try {\n            const response = await fetch(\"/api/keys?id=\".concat(editingApiKey.id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    temperature: editTemperature,\n                    predefined_model_id: editPredefinedModelId\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                // Revert UI on error\n                setSavedKeysWithRoles(previousKeysState);\n                throw new Error(result.details || result.error || 'Failed to update API key');\n            }\n            // Clear cache to ensure fresh data on next fetch\n            clearCache(configId);\n            setSuccessMessage('API key \"'.concat(editingApiKey.label, '\" updated successfully!'));\n            setEditingApiKey(null);\n        } catch (err) {\n            setError(\"Update Key Error: \".concat(err.message));\n        } finally{\n            setIsSavingEdit(false);\n        }\n    };\n    const handleDeleteKey = (keyId, keyLabel)=>{\n        confirmation.showConfirmation({\n            title: 'Delete API Key',\n            message: 'Are you sure you want to delete the API key \"'.concat(keyLabel, '\"? This will permanently remove the key and unassign all its roles. This action cannot be undone.'),\n            confirmText: 'Delete API Key',\n            cancelText: 'Cancel',\n            type: 'danger'\n        }, async ()=>{\n            setIsDeletingKey(keyId);\n            setError(null);\n            setSuccessMessage(null);\n            // Store previous state for rollback on error\n            const previousKeysState = [\n                ...savedKeysWithRoles\n            ];\n            const previousDefaultKeyId = defaultGeneralChatKeyId;\n            const keyToDelete = savedKeysWithRoles.find((key)=>key.id === keyId);\n            // Optimistic UI update - immediately remove the key from the list\n            setSavedKeysWithRoles((prevKeys)=>prevKeys.filter((key)=>key.id !== keyId));\n            // If the deleted key was the default, clear the default\n            if (keyToDelete === null || keyToDelete === void 0 ? void 0 : keyToDelete.is_default_general_chat_model) {\n                setDefaultGeneralChatKeyId(null);\n            }\n            try {\n                const response = await fetch(\"/api/keys/\".concat(keyId), {\n                    method: 'DELETE'\n                });\n                const result = await response.json();\n                if (!response.ok) {\n                    // Revert UI on error\n                    setSavedKeysWithRoles(previousKeysState);\n                    setDefaultGeneralChatKeyId(previousDefaultKeyId);\n                    // Special handling for 404 errors (key already deleted)\n                    if (response.status === 404) {\n                        // Key was already deleted, so the optimistic update was correct\n                        // Don't revert the UI, just show a different message\n                        setSavedKeysWithRoles((prevKeys)=>prevKeys.filter((key)=>key.id !== keyId));\n                        setSuccessMessage('API key \"'.concat(keyLabel, '\" was already deleted.'));\n                        return; // Don't throw error\n                    }\n                    throw new Error(result.details || result.error || 'Failed to delete API key');\n                }\n                // Clear cache to ensure fresh data on next fetch\n                clearCache(configId);\n                setSuccessMessage('API key \"'.concat(keyLabel, '\" deleted successfully!'));\n            } catch (err) {\n                setError(\"Delete Key Error: \".concat(err.message));\n                throw err; // Re-throw to keep modal open on error\n            } finally{\n                setIsDeletingKey(null);\n            }\n        });\n    };\n    const handleSetDefaultChatKey = async (apiKeyIdToSet)=>{\n        if (!configId) return;\n        setError(null);\n        setSuccessMessage(null);\n        const previousKeysState = [\n            ...savedKeysWithRoles\n        ]; // Keep a copy in case of error\n        const previousDefaultKeyId = defaultGeneralChatKeyId;\n        // Optimistic UI update\n        setSavedKeysWithRoles((prevKeys)=>prevKeys.map((key)=>({\n                    ...key,\n                    is_default_general_chat_model: key.id === apiKeyIdToSet\n                })));\n        setDefaultGeneralChatKeyId(apiKeyIdToSet); // Update the separate state for default ID\n        try {\n            const response = await fetch(\"/api/custom-configs/\".concat(configId, \"/default-key-handler/\").concat(apiKeyIdToSet), {\n                method: 'PUT'\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                // Revert UI on error\n                setSavedKeysWithRoles(previousKeysState.map((k)=>({\n                        ...k\n                    }))); // Ensure deep copy for re-render\n                setDefaultGeneralChatKeyId(previousDefaultKeyId);\n                throw new Error(result.details || result.error || 'Failed to set default chat key');\n            }\n            // Clear cache to ensure fresh data on next fetch\n            clearCache(configId);\n            setSuccessMessage(result.message || 'Default general chat key updated!');\n        } catch (err) {\n            setError(\"Set Default Error: \".concat(err.message));\n        }\n    };\n    const handleRoleToggle = async (apiKey, roleId, isAssigned)=>{\n        setError(null);\n        setSuccessMessage(null);\n        const endpoint = \"/api/keys/\".concat(apiKey.id, \"/roles\");\n        // For optimistic update, find role details from combined list (predefined or user's global custom roles)\n        const allAvailableRoles = [\n            ..._config_roles__WEBPACK_IMPORTED_MODULE_4__.PREDEFINED_ROLES.map((r)=>({\n                    ...r,\n                    isCustom: false\n                })),\n            ...userCustomRoles.map((cr)=>({\n                    id: cr.role_id,\n                    name: cr.name,\n                    description: cr.description || undefined,\n                    isCustom: true,\n                    databaseId: cr.id\n                }))\n        ];\n        const roleDetails = allAvailableRoles.find((r)=>r.id === roleId) || {\n            id: roleId,\n            name: roleId,\n            description: ''\n        };\n        const previousKeysState = savedKeysWithRoles.map((k)=>({\n                ...k,\n                assigned_roles: [\n                    ...k.assigned_roles.map((r)=>({\n                            ...r\n                        }))\n                ]\n            })); // Deep copy\n        let previousEditingRolesApiKey = null;\n        if (editingRolesApiKey && editingRolesApiKey.id === apiKey.id) {\n            previousEditingRolesApiKey = {\n                ...editingRolesApiKey,\n                assigned_roles: [\n                    ...editingRolesApiKey.assigned_roles.map((r)=>({\n                            ...r\n                        }))\n                ]\n            };\n        }\n        // Optimistic UI update\n        setSavedKeysWithRoles((prevKeys)=>prevKeys.map((key)=>{\n                if (key.id === apiKey.id) {\n                    const updatedRoles = isAssigned ? key.assigned_roles.filter((r)=>r.id !== roleId) : [\n                        ...key.assigned_roles,\n                        roleDetails\n                    ];\n                    return {\n                        ...key,\n                        assigned_roles: updatedRoles\n                    };\n                }\n                return key;\n            }));\n        if (editingRolesApiKey && editingRolesApiKey.id === apiKey.id) {\n            setEditingRolesApiKey((prevEditingKey)=>{\n                if (!prevEditingKey) return null;\n                const updatedRoles = isAssigned ? prevEditingKey.assigned_roles.filter((r)=>r.id !== roleId) : [\n                    ...prevEditingKey.assigned_roles,\n                    roleDetails\n                ];\n                return {\n                    ...prevEditingKey,\n                    assigned_roles: updatedRoles\n                };\n            });\n        }\n        try {\n            let response;\n            if (isAssigned) {\n                response = await fetch(\"\".concat(endpoint, \"/\").concat(roleId), {\n                    method: 'DELETE'\n                });\n            } else {\n                response = await fetch(endpoint, {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        role_name: roleId\n                    })\n                });\n            }\n            const result = await response.json();\n            if (!response.ok) {\n                // Revert UI on error\n                setSavedKeysWithRoles(previousKeysState);\n                if (previousEditingRolesApiKey) {\n                    setEditingRolesApiKey(previousEditingRolesApiKey);\n                } else if (editingRolesApiKey && editingRolesApiKey.id === apiKey.id) {\n                    const originalKeyData = previousKeysState.find((k)=>k.id === apiKey.id);\n                    if (originalKeyData) setEditingRolesApiKey(originalKeyData);\n                }\n                // Use the error message from the backend if available (e.g., for 409 conflict)\n                const errorMessage = response.status === 409 && result.error ? result.error : result.details || result.error || (isAssigned ? 'Failed to unassign role' : 'Failed to assign role');\n                throw new Error(errorMessage);\n            }\n            setSuccessMessage(result.message || \"Role '\".concat(roleDetails.name, \"' \").concat(isAssigned ? 'unassigned' : 'assigned', \" successfully.\"));\n        } catch (err) {\n            // err.message now contains the potentially more user-friendly message from the backend or a fallback\n            setError(\"Role Update Error: \".concat(err.message));\n        }\n    };\n    const handleCreateCustomRole = async ()=>{\n        // Removed editingRolesApiKey check as creating a global role isn't tied to a specific key being edited.\n        // configId is also not needed for creating a global role.\n        if (!newCustomRoleId.trim() || newCustomRoleId.trim().length > 30 || !/^[a-zA-Z0-9_]+$/.test(newCustomRoleId.trim())) {\n            setCreateCustomRoleError('Role ID is required (max 30 chars, letters, numbers, underscores only).');\n            return;\n        }\n        // Check against PREDEFINED_ROLES and the user's existing global custom roles\n        if (_config_roles__WEBPACK_IMPORTED_MODULE_4__.PREDEFINED_ROLES.some((pr)=>pr.id.toLowerCase() === newCustomRoleId.trim().toLowerCase()) || userCustomRoles.some((cr)=>cr.role_id.toLowerCase() === newCustomRoleId.trim().toLowerCase())) {\n            setCreateCustomRoleError('This Role ID is already in use (either predefined or as one of your custom roles).');\n            return;\n        }\n        if (!newCustomRoleName.trim()) {\n            setCreateCustomRoleError('Role Name is required.');\n            return;\n        }\n        setCreateCustomRoleError(null);\n        setIsSavingCustomRole(true);\n        try {\n            const response = await fetch(\"/api/user/custom-roles\", {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    role_id: newCustomRoleId.trim(),\n                    name: newCustomRoleName.trim(),\n                    description: newCustomRoleDescription.trim()\n                })\n            });\n            if (!response.ok) {\n                // Try to parse the error response as JSON, but fallback if it's not JSON\n                let errorResult;\n                try {\n                    errorResult = await response.json();\n                } catch (parseError) {\n                    // If JSON parsing fails, use the response text or a generic status message\n                    const errorText = await response.text().catch(()=>\"HTTP status \".concat(response.status));\n                    errorResult = {\n                        error: \"Server error, could not parse response.\",\n                        details: errorText\n                    };\n                }\n                let displayError = errorResult.error || 'Failed to create custom role.';\n                if (errorResult.details) {\n                    displayError += \" (Details: \".concat(errorResult.details, \")\");\n                } else if (errorResult.issues) {\n                    // If Zod issues, format them for better readability\n                    const issuesString = Object.entries(errorResult.issues).map((param)=>{\n                        let [field, messages] = param;\n                        return \"\".concat(field, \": \").concat(messages.join(', '));\n                    }).join('; ');\n                    displayError += \" (Issues: \".concat(issuesString, \")\");\n                }\n                throw new Error(displayError);\n            }\n            // If response IS ok, then parse the successful JSON response\n            const result = await response.json();\n            setNewCustomRoleId('');\n            setNewCustomRoleName('');\n            setNewCustomRoleDescription('');\n            // setShowCreateCustomRoleForm(false); // User might want to add multiple roles\n            // Clear cache to ensure fresh data on next fetch\n            clearCache(configId);\n            // Add the new role optimistically to the local state\n            const newRole = {\n                id: result.id,\n                role_id: result.role_id,\n                name: result.name,\n                description: result.description,\n                user_id: result.user_id,\n                created_at: result.created_at,\n                updated_at: result.updated_at\n            };\n            setUserCustomRoles((prev)=>[\n                    ...prev,\n                    newRole\n                ]);\n            setSuccessMessage(\"Custom role '\".concat(result.name, \"' created successfully! It is now available globally.\"));\n        } catch (err) {\n            setCreateCustomRoleError(err.message);\n        } finally{\n            setIsSavingCustomRole(false);\n        }\n    };\n    const handleDeleteCustomRole = (customRoleDatabaseId, customRoleName)=>{\n        // configId is not needed for deleting a global role\n        if (!customRoleDatabaseId) return;\n        confirmation.showConfirmation({\n            title: 'Delete Custom Role',\n            message: 'Are you sure you want to delete the custom role \"'.concat(customRoleName, \"\\\"? This will unassign it from all API keys where it's currently used. This action cannot be undone.\"),\n            confirmText: 'Delete Role',\n            cancelText: 'Cancel',\n            type: 'danger'\n        }, async ()=>{\n            setDeletingCustomRoleId(customRoleDatabaseId);\n            setUserCustomRolesError(null);\n            setCreateCustomRoleError(null);\n            setSuccessMessage(null);\n            try {\n                const response = await fetch(\"/api/user/custom-roles/\".concat(customRoleDatabaseId), {\n                    method: 'DELETE'\n                });\n                const result = await response.json(); // Try to parse JSON for all responses\n                if (!response.ok) {\n                    throw new Error(result.error || 'Failed to delete custom role');\n                }\n                // Optimistically remove from the local state\n                setUserCustomRoles((prev)=>prev.filter((role)=>role.id !== customRoleDatabaseId));\n                // Clear cache to ensure fresh data on next fetch\n                clearCache(configId);\n                setSuccessMessage(result.message || 'Global custom role \"'.concat(customRoleName, '\" deleted successfully.'));\n                // Re-fetch keys and roles for the current config, as the deleted global role might have been assigned here.\n                // This ensures the displayed assigned roles for keys on this page are up-to-date.\n                if (configId) {\n                    fetchKeysAndRolesForConfig();\n                }\n            } catch (err) {\n                setUserCustomRolesError(\"Error deleting role: \".concat(err.message));\n                throw err; // Re-throw to keep modal open on error\n            } finally{\n                setDeletingCustomRoleId(null);\n            }\n        });\n    };\n    const renderManageRolesModal = ()=>{\n        if (!editingRolesApiKey) return null;\n        const combinedRoles = [\n            ..._config_roles__WEBPACK_IMPORTED_MODULE_4__.PREDEFINED_ROLES.map((r)=>({\n                    ...r,\n                    isCustom: false\n                })),\n            ...userCustomRoles.map((cr)=>({\n                    id: cr.role_id,\n                    name: cr.name,\n                    description: cr.description || undefined,\n                    isCustom: true,\n                    databaseId: cr.id // The actual DB ID (UUID) for delete operations\n                }))\n        ].sort((a, b)=>a.name.localeCompare(b.name));\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-900/95 backdrop-blur-sm border border-gray-800/50 rounded-lg w-full max-w-lg max-h-[90vh] flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center p-6 border-b border-gray-800/50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-white\",\n                                children: [\n                                    \"Manage Roles for: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-orange-400\",\n                                        children: editingRolesApiKey.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 985,\n                                        columnNumber: 80\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 985,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setEditingRolesApiKey(null);\n                                    setShowCreateCustomRoleForm(false);\n                                    setCreateCustomRoleError(null);\n                                },\n                                className: \"text-gray-400 hover:text-white hover:bg-gray-800/50 p-1 rounded-lg transition-colors duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 987,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 986,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 984,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-b border-gray-200\",\n                        children: [\n                            userCustomRolesError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 border border-red-200 rounded-lg p-3 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-800 text-sm\",\n                                    children: [\n                                        \"Error with custom roles: \",\n                                        userCustomRolesError\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 994,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 993,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_13__.TierGuard, {\n                                feature: \"custom_roles\",\n                                customMessage: \"Custom roles are available starting with the Starter plan. Create specialized roles to organize your API keys by task type and optimize routing for different use cases.\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowCreateCustomRoleForm(!showCreateCustomRoleForm),\n                                            className: \"btn-primary text-sm inline-flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1007,\n                                                    columnNumber: 19\n                                                }, this),\n                                                showCreateCustomRoleForm ? 'Cancel New Role' : 'Create New Custom Role'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1003,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1002,\n                                        columnNumber: 15\n                                    }, this),\n                                    showCreateCustomRoleForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-800/50 border border-gray-700/50 rounded-lg p-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-md font-medium text-white mb-3\",\n                                                children: \"Create New Custom Role for this Configuration\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1014,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"newCustomRoleId\",\n                                                                className: \"block text-sm font-medium text-gray-300 mb-1\",\n                                                                children: \"Role ID (short, no spaces, max 30 chars)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1017,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                id: \"newCustomRoleId\",\n                                                                value: newCustomRoleId,\n                                                                onChange: (e)=>setNewCustomRoleId(e.target.value.replace(/\\s/g, '')),\n                                                                className: \"w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\",\n                                                                maxLength: 30,\n                                                                placeholder: \"e.g., my_blog_writer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1018,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1016,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"newCustomRoleName\",\n                                                                className: \"block text-sm font-medium text-gray-300 mb-1\",\n                                                                children: \"Display Name (max 100 chars)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1027,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                id: \"newCustomRoleName\",\n                                                                value: newCustomRoleName,\n                                                                onChange: (e)=>setNewCustomRoleName(e.target.value),\n                                                                className: \"w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\",\n                                                                maxLength: 100,\n                                                                placeholder: \"e.g., My Awesome Blog Writer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1028,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1026,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"newCustomRoleDescription\",\n                                                                className: \"block text-sm font-medium text-gray-300 mb-1\",\n                                                                children: \"Description (optional, max 500 chars)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1037,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                id: \"newCustomRoleDescription\",\n                                                                value: newCustomRoleDescription,\n                                                                onChange: (e)=>setNewCustomRoleDescription(e.target.value),\n                                                                rows: 2,\n                                                                className: \"w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\",\n                                                                maxLength: 500,\n                                                                placeholder: \"Optional: Describe what this role is for...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1038,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1036,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    createCustomRoleError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-red-900/50 border border-red-800/50 rounded-lg p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-300 text-sm\",\n                                                            children: createCustomRoleError\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1049,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1048,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleCreateCustomRole,\n                                                        disabled: isSavingCustomRole,\n                                                        className: \"btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                        children: isSavingCustomRole ? 'Saving Role...' : 'Save Custom Role'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1052,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1015,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1013,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 998,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 991,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-gray-300 mb-3\",\n                                children: \"Select roles to assign:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1066,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-y-auto space-y-2\",\n                                style: {\n                                    maxHeight: 'calc(90vh - 350px)'\n                                },\n                                children: [\n                                    isLoadingUserCustomRoles && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center py-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-orange-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1070,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm ml-2\",\n                                                children: \"Loading custom roles...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1071,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1069,\n                                        columnNumber: 17\n                                    }, this),\n                                    combinedRoles.map((role)=>{\n                                        const isAssigned = editingRolesApiKey.assigned_roles.some((ar)=>ar.id === role.id);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-3 rounded-lg border transition-all duration-200 \".concat(isAssigned ? 'bg-orange-500/20 border-orange-500/30 shadow-sm' : 'bg-gray-800/50 border-gray-700/50 hover:border-gray-600/50 hover:shadow-sm'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"role-\".concat(role.id),\n                                                    className: \"flex items-center cursor-pointer flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            id: \"role-\".concat(role.id),\n                                                            checked: isAssigned,\n                                                            onChange: ()=>handleRoleToggle(editingRolesApiKey, role.id, isAssigned),\n                                                            className: \"h-4 w-4 text-orange-500 border-gray-600 rounded focus:ring-orange-500 focus:ring-2 cursor-pointer bg-gray-700\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1083,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-3 text-sm font-medium \".concat(isAssigned ? 'text-orange-300' : 'text-white'),\n                                                            children: role.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1090,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        role.isCustom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-500/20 text-blue-300\",\n                                                            children: \"Custom\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1094,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1082,\n                                                    columnNumber: 21\n                                                }, this),\n                                                role.isCustom && role.databaseId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDeleteCustomRole(role.databaseId, role.name),\n                                                    disabled: deletingCustomRoleId === role.databaseId,\n                                                    className: \"p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-wait ml-2\",\n                                                    title: \"Delete this custom role\",\n                                                    children: deletingCustomRoleId === role.databaseId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-4 w-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1106,\n                                                        columnNumber: 70\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1106,\n                                                        columnNumber: 123\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1100,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, role.id, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1077,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1067,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1065,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-t border-gray-200 bg-gray-50 rounded-b-xl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setEditingRolesApiKey(null);\n                                    setShowCreateCustomRoleForm(false);\n                                    setCreateCustomRoleError(null);\n                                },\n                                className: \"btn-secondary\",\n                                children: \"Done\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1117,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1116,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1115,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                lineNumber: 983,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n            lineNumber: 982,\n            columnNumber: 7\n        }, this);\n    };\n    // Main render logic with optimistic loading\n    if (showOptimisticLoading && !isCached(configId)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ManageKeysLoadingSkeleton__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n            lineNumber: 1132,\n            columnNumber: 12\n        }, this);\n    }\n    if (isLoadingConfig && !configDetails) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ManageKeysLoadingSkeleton__WEBPACK_IMPORTED_MODULE_9__.CompactManageKeysLoadingSkeleton, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n            lineNumber: 1136,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full px-4 sm:px-6 lg:px-8 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>navigateOptimistically('/my-models'),\n                            className: \"text-orange-400 hover:text-orange-300 inline-flex items-center mb-6 transition-colors duration-200 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"h-5 w-5 mr-2 group-hover:-translate-x-1 transition-transform\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1148,\n                                    columnNumber: 13\n                                }, this),\n                                \"Back to My API Models\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1144,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-8 mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: configDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-6 w-6 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1160,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1159,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                    className: \"text-3xl font-bold text-white\",\n                                                                    children: configDetails.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1163,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-400 mt-1\",\n                                                                    children: \"Model Configuration\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1166,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1162,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1158,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-sm text-gray-300 bg-gray-800/50 px-4 py-2 rounded-lg w-fit\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-block w-2 h-2 bg-orange-500 rounded-full mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1170,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"ID: \",\n                                                        configDetails.id\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1169,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : error && !isLoadingConfig ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-red-900/50 rounded-2xl flex items-center justify-center mr-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-6 w-6 text-red-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1177,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1176,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-2xl font-bold text-red-400\",\n                                                            children: \"Configuration Error\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1180,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-300 mt-1\",\n                                                            children: error.replace(\"Error loading model configuration: \", \"\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1181,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1179,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1175,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gray-800/50 rounded-2xl flex items-center justify-center mr-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-6 w-6 text-gray-400 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1187,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1186,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-2xl font-bold text-white\",\n                                                            children: \"Loading Configuration...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1190,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 mt-1\",\n                                                            children: \"Please wait while we fetch your model details\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1191,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1189,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1185,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1155,\n                                        columnNumber: 13\n                                    }, this),\n                                    configDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>navigateOptimistically(\"/routing-setup/\".concat(configId, \"?from=model-config\")),\n                                            className: \"inline-flex items-center px-6 py-3 text-sm font-semibold rounded-2xl shadow-sm text-white bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 group\",\n                                            ...createRoutingHoverPrefetch(configId),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2 group-hover:rotate-90 transition-transform duration-200\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1208,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Advanced Routing Setup\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1203,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1199,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1154,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1153,\n                            columnNumber: 11\n                        }, this),\n                        successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-green-900/50 backdrop-blur-sm border border-green-800/50 rounded-lg p-4 mb-6 animate-slide-in\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-5 w-5 text-green-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1220,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-300 font-medium\",\n                                        children: successMessage\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1221,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1219,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1218,\n                            columnNumber: 13\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-900/50 backdrop-blur-sm border border-red-800/50 rounded-lg p-4 mb-6 animate-slide-in\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-5 w-5 text-red-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1228,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-300 font-medium\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1229,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1227,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1226,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1143,\n                    columnNumber: 9\n                }, this),\n                configDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab('provider-keys'),\n                                        className: \"flex-1 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 \".concat(activeTab === 'provider-keys' ? 'bg-orange-500 text-white shadow-md' : 'text-gray-400 hover:text-white hover:bg-gray-800/50'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1249,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Provider API Keys\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1250,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1248,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1240,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab('user-api-keys'),\n                                        className: \"flex-1 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 \".concat(activeTab === 'user-api-keys' ? 'bg-orange-500 text-white shadow-md' : 'text-gray-400 hover:text-white hover:bg-gray-800/50'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1262,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Generated API Keys\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1263,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1261,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1253,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1239,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1238,\n                            columnNumber: 11\n                        }, this),\n                        activeTab === 'provider-keys' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 xl:grid-cols-5 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"xl:col-span-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6 sticky top-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mr-3 shadow-md\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"h-5 w-5 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1277,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1276,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-xl font-bold text-white\",\n                                                                children: \"Add Provider API Key\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1280,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"Configure new provider key\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1281,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1279,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1275,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                onSubmit: handleSaveKey,\n                                                className: \"space-y-5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"provider\",\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: \"Provider\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1288,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        id: \"provider\",\n                                                                        value: provider,\n                                                                        onChange: (e)=>{\n                                                                            setProvider(e.target.value);\n                                                                        },\n                                                                        className: \"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white text-sm\",\n                                                                        children: PROVIDER_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: option.value,\n                                                                                className: \"bg-gray-800 text-white\",\n                                                                                children: option.label\n                                                                            }, option.value, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1298,\n                                                                                columnNumber: 25\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1291,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1287,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"apiKeyRaw\",\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: \"API Key\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1304,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"apiKeyRaw\",\n                                                                        type: \"password\",\n                                                                        value: apiKeyRaw,\n                                                                        onChange: (e)=>setApiKeyRaw(e.target.value),\n                                                                        className: \"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white placeholder-gray-400 text-sm\",\n                                                                        placeholder: \"Enter your API key\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1307,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    isFetchingProviderModels && fetchedProviderModels === null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"mt-2 text-xs text-orange-600 flex items-center bg-orange-50 p-2 rounded-lg\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-1 animate-pulse\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1318,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            \"Fetching models...\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1317,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    fetchProviderModelsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"mt-2 text-xs text-red-600 bg-red-50 p-2 rounded-lg\",\n                                                                        children: fetchProviderModelsError\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1323,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1303,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"predefinedModelId\",\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"Model Variant\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1328,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        id: \"predefinedModelId\",\n                                                                        value: predefinedModelId,\n                                                                        onChange: (e)=>setPredefinedModelId(e.target.value),\n                                                                        disabled: !modelOptions.length,\n                                                                        className: \"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm disabled:bg-gray-50 disabled:text-gray-500\",\n                                                                        children: modelOptions.length > 0 ? modelOptions.map((m)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: m.value,\n                                                                                children: m.label\n                                                                            }, m.value, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1340,\n                                                                                columnNumber: 27\n                                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            disabled: true,\n                                                                            children: fetchedProviderModels === null && isFetchingProviderModels ? \"Loading models...\" : \"Select a provider first\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1343,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1331,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1327,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"label\",\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"Label\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1349,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        id: \"label\",\n                                                                        value: label,\n                                                                        onChange: (e)=>setLabel(e.target.value),\n                                                                        required: true,\n                                                                        className: \"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm\",\n                                                                        placeholder: \"e.g., My OpenAI GPT-4o Key #1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1352,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1348,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"temperature\",\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: [\n                                                                            \"Temperature\",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-gray-500 ml-1\",\n                                                                                children: \"(0.0 - 2.0)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1366,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1364,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"range\",\n                                                                                id: \"temperature\",\n                                                                                min: \"0\",\n                                                                                max: \"2\",\n                                                                                step: \"0.1\",\n                                                                                value: temperature,\n                                                                                onChange: (e)=>setTemperature(parseFloat(e.target.value)),\n                                                                                className: \"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider-orange\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1369,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs text-gray-500\",\n                                                                                        children: \"Conservative\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                        lineNumber: 1380,\n                                                                                        columnNumber: 25\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center space-x-2\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"number\",\n                                                                                            min: \"0\",\n                                                                                            max: \"2\",\n                                                                                            step: \"0.1\",\n                                                                                            value: temperature,\n                                                                                            onChange: (e)=>setTemperature(Math.min(2.0, Math.max(0.0, parseFloat(e.target.value) || 0))),\n                                                                                            className: \"w-16 px-2 py-1 text-xs border border-gray-200 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                            lineNumber: 1382,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                        lineNumber: 1381,\n                                                                                        columnNumber: 25\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs text-gray-500\",\n                                                                                        children: \"Creative\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                        lineNumber: 1392,\n                                                                                        columnNumber: 25\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1379,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-gray-500\",\n                                                                                children: \"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1394,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1368,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1363,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1286,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: isSavingKey || !predefinedModelId || predefinedModelId === '' || !apiKeyRaw.trim() || !label.trim(),\n                                                        className: \"w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-none text-sm\",\n                                                        children: isSavingKey ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2 animate-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1408,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Saving...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1407,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1413,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Add API Key\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1412,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1401,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1285,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6 p-4 bg-blue-900/50 backdrop-blur-sm border border-blue-800/50 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            className: \"h-5 w-5 text-blue-400 mt-0.5 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1423,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-sm font-medium text-blue-300 mb-1\",\n                                                                    children: \"Key Configuration Rules\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1425,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-blue-200 space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: [\n                                                                                \"✅ \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Same API key, different models:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1427,\n                                                                                    columnNumber: 28\n                                                                                }, this),\n                                                                                \" Allowed\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1427,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: [\n                                                                                \"✅ \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Different API keys, same model:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1428,\n                                                                                    columnNumber: 28\n                                                                                }, this),\n                                                                                \" Allowed\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1428,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: [\n                                                                                \"❌ \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Same model twice:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1429,\n                                                                                    columnNumber: 28\n                                                                                }, this),\n                                                                                \" Not allowed in one configuration\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1429,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1426,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1424,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1422,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1421,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1274,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1273,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"xl:col-span-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-3 shadow-md\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-5 w-5 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1442,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1441,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-xl font-bold text-white\",\n                                                                children: \"API Keys & Roles\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1445,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"Manage existing keys\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1446,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1444,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1440,\n                                                columnNumber: 15\n                                            }, this),\n                                            isLoadingKeysAndRoles && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-8 w-8 text-gray-400 mx-auto mb-3 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1452,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"Loading API keys...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1453,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1451,\n                                                columnNumber: 17\n                                            }, this),\n                                            !isLoadingKeysAndRoles && savedKeysWithRoles.length === 0 && (!error || error && error.startsWith(\"Error loading model configuration:\")) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-6 w-6 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1460,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1459,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-semibold text-gray-900 mb-1\",\n                                                        children: \"No API Keys\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1462,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"Add your first key using the form\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1463,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1458,\n                                                columnNumber: 17\n                                            }, this),\n                                            !isLoadingKeysAndRoles && savedKeysWithRoles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 max-h-96 overflow-y-auto\",\n                                                children: savedKeysWithRoles.map((key, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-50 border border-gray-200 rounded-xl p-4 hover:shadow-md transition-all duration-200 animate-slide-in\",\n                                                        style: {\n                                                            animationDelay: \"\".concat(index * 50, \"ms\")\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 min-w-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center mb-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                    className: \"text-sm font-semibold text-gray-900 truncate mr-2\",\n                                                                                    children: key.label\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1474,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                key.is_default_general_chat_model && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 flex-shrink-0\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                            className: \"h-3 w-3 mr-1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                            lineNumber: 1477,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        \"Default\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1476,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1473,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-gray-900 bg-white px-2 py-1 rounded-lg border\",\n                                                                                            children: [\n                                                                                                key.provider,\n                                                                                                \" (\",\n                                                                                                key.predefined_model_id,\n                                                                                                \")\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                            lineNumber: 1485,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded-lg border border-orange-200\",\n                                                                                            children: [\n                                                                                                \"Temp: \",\n                                                                                                key.temperature\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                            lineNumber: 1488,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1484,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_13__.TierGuard, {\n                                                                                    feature: \"custom_roles\",\n                                                                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex flex-wrap gap-1\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-xs text-gray-400 bg-gray-50 px-2 py-1 rounded-lg border border-gray-200\",\n                                                                                            children: \"Roles available on Starter plan+\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                            lineNumber: 1497,\n                                                                                            columnNumber: 35\n                                                                                        }, void 0)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                        lineNumber: 1496,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex flex-wrap gap-1\",\n                                                                                        children: key.assigned_roles.length > 0 ? key.assigned_roles.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"inline-block whitespace-nowrap rounded-full bg-orange-100 px-2 py-1 text-xs font-medium text-orange-800\",\n                                                                                                children: role.name\n                                                                                            }, role.id, false, {\n                                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                                lineNumber: 1506,\n                                                                                                columnNumber: 37\n                                                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-xs text-gray-500 bg-white px-2 py-1 rounded-lg border\",\n                                                                                            children: \"No roles\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                            lineNumber: 1511,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                        lineNumber: 1503,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1493,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1483,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        !key.is_default_general_chat_model && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleSetDefaultChatKey(key.id),\n                                                                            className: \"text-xs bg-white border border-gray-200 hover:border-gray-300 text-gray-700 hover:text-gray-900 py-1 px-2 rounded-lg mt-2 transition-colors\",\n                                                                            \"data-tooltip-id\": \"global-tooltip\",\n                                                                            \"data-tooltip-content\": \"Set as default chat model\",\n                                                                            children: \"Set Default\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1518,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1472,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-1 ml-2 flex-shrink-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleEditKey(key),\n                                                                            disabled: isDeletingKey === key.id,\n                                                                            className: \"p-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors disabled:opacity-50\",\n                                                                            \"data-tooltip-id\": \"global-tooltip\",\n                                                                            \"data-tooltip-content\": \"Edit Model & Settings\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1537,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1530,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_13__.TierGuard, {\n                                                                            feature: \"custom_roles\",\n                                                                            fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                disabled: true,\n                                                                                className: \"p-2 text-gray-400 cursor-not-allowed rounded-lg opacity-50\",\n                                                                                \"data-tooltip-id\": \"global-tooltip\",\n                                                                                \"data-tooltip-content\": \"Role management requires Starter plan or higher\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1548,\n                                                                                    columnNumber: 33\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1542,\n                                                                                columnNumber: 31\n                                                                            }, void 0),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>setEditingRolesApiKey(key),\n                                                                                disabled: isDeletingKey === key.id,\n                                                                                className: \"p-2 text-orange-600 hover:text-orange-700 hover:bg-orange-50 rounded-lg transition-colors disabled:opacity-50\",\n                                                                                \"data-tooltip-id\": \"global-tooltip\",\n                                                                                \"data-tooltip-content\": \"Manage Roles\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1559,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1552,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1539,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleDeleteKey(key.id, key.label),\n                                                                            disabled: isDeletingKey === key.id,\n                                                                            className: \"p-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50\",\n                                                                            \"data-tooltip-id\": \"global-tooltip\",\n                                                                            \"data-tooltip-content\": \"Delete Key\",\n                                                                            children: isDeletingKey === key.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-4 w-4 animate-pulse\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1570,\n                                                                                columnNumber: 31\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1572,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1562,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1529,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1471,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, key.id, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1470,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1468,\n                                                columnNumber: 17\n                                            }, this),\n                                            !isLoadingKeysAndRoles && error && !error.startsWith(\"Error loading model configuration:\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-red-50 border border-red-200 rounded-xl p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-5 w-5 text-red-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1585,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-800 font-medium text-sm\",\n                                                            children: [\n                                                                \"Could not load API keys/roles: \",\n                                                                error\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1586,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1584,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1583,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1439,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1438,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1271,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === 'user-api-keys' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UserApiKeys_ApiKeyManager__WEBPACK_IMPORTED_MODULE_12__.ApiKeyManager, {\n                                configId: configId,\n                                configName: configDetails.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1598,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1597,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1236,\n                    columnNumber: 9\n                }, this),\n                editingRolesApiKey && renderManageRolesModal(),\n                editingApiKey && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card w-full max-w-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-6 border-b border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"Edit API Key\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1615,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setEditingApiKey(null),\n                                        className: \"text-gray-500 hover:text-gray-700 hover:bg-gray-100 p-1 rounded-lg transition-colors duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1620,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1616,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1614,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                children: editingApiKey.label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1626,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: [\n                                                    \"Current: \",\n                                                    editingApiKey.provider,\n                                                    \" (\",\n                                                    editingApiKey.predefined_model_id,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1627,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1625,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"Provider\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1634,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full p-2.5 bg-gray-50 border border-gray-300 rounded-md text-gray-700\",\n                                                        children: ((_llmProviders_find = _config_models__WEBPACK_IMPORTED_MODULE_3__.llmProviders.find((p)=>p.id === editingApiKey.provider)) === null || _llmProviders_find === void 0 ? void 0 : _llmProviders_find.name) || editingApiKey.provider\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1637,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                        children: \"Provider cannot be changed\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1640,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1633,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"editModelId\",\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"Model\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1644,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"editModelId\",\n                                                        value: editPredefinedModelId,\n                                                        onChange: (e)=>setEditPredefinedModelId(e.target.value),\n                                                        disabled: !editModelOptions.length,\n                                                        className: \"w-full p-2.5 bg-white border border-gray-300 rounded-md text-gray-900 focus:ring-orange-500 focus:border-orange-500 disabled:opacity-50 disabled:bg-gray-100\",\n                                                        children: editModelOptions.length > 0 ? editModelOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: option.value,\n                                                                children: option.label\n                                                            }, option.value, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1656,\n                                                                columnNumber: 25\n                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            disabled: true,\n                                                            children: isFetchingProviderModels ? 'Loading models...' : 'No models available'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1661,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1647,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1643,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"editTemperature\",\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: [\n                                                            \"Temperature: \",\n                                                            editTemperature\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1669,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"range\",\n                                                        id: \"editTemperature\",\n                                                        min: \"0\",\n                                                        max: \"2\",\n                                                        step: \"0.1\",\n                                                        value: editTemperature,\n                                                        onChange: (e)=>setEditTemperature(parseFloat(e.target.value)),\n                                                        className: \"slider-orange w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1672,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-xs text-gray-500 mt-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"0.0 (Focused)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1683,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"1.0 (Balanced)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1684,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"2.0 (Creative)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1685,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1682,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1668,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 rounded-lg p-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600\",\n                                                    children: \"You can change the model and temperature settings. Temperature controls randomness in responses. Lower values (0.0-0.3) are more focused and deterministic, while higher values (1.5-2.0) are more creative and varied.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1690,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1689,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1632,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1624,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-t border-gray-200 bg-gray-50 rounded-b-xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setEditingApiKey(null),\n                                            className: \"btn-secondary\",\n                                            disabled: isSavingEdit,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1699,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleSaveEdit,\n                                            disabled: isSavingEdit,\n                                            className: \"btn-primary\",\n                                            children: isSavingEdit ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1713,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Saving...\"\n                                                ]\n                                            }, void 0, true) : 'Save Changes'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1706,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1698,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1697,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1613,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1612,\n                    columnNumber: 9\n                }, this),\n                !configDetails && !isLoadingConfig && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-sm border border-gray-100 text-center py-16 px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                className: \"h-8 w-8 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1729,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1728,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-3\",\n                            children: \"Model Not Found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1731,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600 mb-8\",\n                            children: \"This API Model configuration could not be found or may have been deleted.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1732,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>navigateOptimistically('/my-models'),\n                            className: \"inline-flex items-center px-6 py-3 text-sm font-medium rounded-xl text-white bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1737,\n                                    columnNumber: 13\n                                }, this),\n                                \"Return to My API Models\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1733,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1727,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    isOpen: confirmation.isOpen,\n                    onClose: confirmation.hideConfirmation,\n                    onConfirm: confirmation.onConfirm,\n                    title: confirmation.title,\n                    message: confirmation.message,\n                    confirmText: confirmation.confirmText,\n                    cancelText: confirmation.cancelText,\n                    type: confirmation.type,\n                    isLoading: confirmation.isLoading\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1744,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tooltip__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                    id: \"global-tooltip\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1756,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n            lineNumber: 1141,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n        lineNumber: 1140,\n        columnNumber: 5\n    }, this);\n}\n_s(ConfigDetailsPage, \"phTjtpqKEcxfO5MxUFWlO/AjVB0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_7__.useConfirmation,\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_11__.useNavigationSafe,\n        _hooks_useManageKeysPrefetch__WEBPACK_IMPORTED_MODULE_8__.useManageKeysPrefetch,\n        _hooks_useRoutingSetupPrefetch__WEBPACK_IMPORTED_MODULE_10__.useRoutingSetupPrefetch\n    ];\n});\n_c2 = ConfigDetailsPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"PROVIDER_OPTIONS$llmProviders.map\");\n$RefreshReg$(_c1, \"PROVIDER_OPTIONS\");\n$RefreshReg$(_c2, \"ConfigDetailsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/my-models/[configId]/page.tsx\n"));

/***/ })

});