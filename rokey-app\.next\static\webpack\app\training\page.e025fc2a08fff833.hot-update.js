"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/training/page",{

/***/ "(app-pages-browser)/./src/app/training/page.tsx":
/*!***********************************!*\
  !*** ./src/app/training/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TrainingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_DocumentUpload__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/DocumentUpload */ \"(app-pages-browser)/./src/components/DocumentUpload.tsx\");\n/* harmony import */ var _components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/TierEnforcement */ \"(app-pages-browser)/./src/components/TierEnforcement/index.ts\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* harmony import */ var _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useConfirmation */ \"(app-pages-browser)/./src/hooks/useConfirmation.ts\");\n/* harmony import */ var _components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/ConfirmationModal */ \"(app-pages-browser)/./src/components/ui/ConfirmationModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction TrainingPage() {\n    _s();\n    // Subscription hook\n    const { subscriptionStatus } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_4__.useSubscription)();\n    const confirmation = (0,_hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_5__.useConfirmation)();\n    // State management\n    const [customConfigs, setCustomConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedConfigId, setSelectedConfigId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [trainingJobs, setTrainingJobs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [documentCount, setDocumentCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Prompt engineering form state\n    const [trainingPrompts, setTrainingPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Load existing training data for a configuration\n    const loadExistingTrainingData = async (configId)=>{\n        if (!configId) return;\n        try {\n            // Load training jobs\n            const jobsResponse = await fetch(\"/api/training/jobs?custom_api_config_id=\".concat(configId));\n            if (jobsResponse.ok) {\n                const jobs = await jobsResponse.json();\n                if (jobs.length > 0) {\n                    var _latestJob_training_data;\n                    const latestJob = jobs[0];\n                    // Load training prompts\n                    if ((_latestJob_training_data = latestJob.training_data) === null || _latestJob_training_data === void 0 ? void 0 : _latestJob_training_data.raw_prompts) {\n                        setTrainingPrompts(latestJob.training_data.raw_prompts);\n                    }\n                }\n            }\n        } catch (err) {\n            console.warn('Failed to load existing training data:', err);\n        }\n    };\n    // Fetch custom API configs on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TrainingPage.useEffect\": ()=>{\n            const fetchConfigs = {\n                \"TrainingPage.useEffect.fetchConfigs\": async ()=>{\n                    try {\n                        const response = await fetch('/api/custom-configs');\n                        if (!response.ok) {\n                            throw new Error('Failed to fetch configurations');\n                        }\n                        const data = await response.json();\n                        setCustomConfigs(data);\n                        if (data.length > 0) {\n                            setSelectedConfigId(data[0].id);\n                            loadExistingTrainingData(data[0].id);\n                        }\n                    } catch (err) {\n                        setError(\"Failed to load configurations: \".concat(err.message));\n                    }\n                }\n            }[\"TrainingPage.useEffect.fetchConfigs\"];\n            fetchConfigs();\n        }\n    }[\"TrainingPage.useEffect\"], []);\n    // Load training data when configuration changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TrainingPage.useEffect\": ()=>{\n            if (selectedConfigId) {\n                loadExistingTrainingData(selectedConfigId);\n            }\n        }\n    }[\"TrainingPage.useEffect\"], [\n        selectedConfigId\n    ]);\n    // Fetch document count for a configuration\n    const fetchDocumentCount = async function(configId) {\n        let retryCount = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        try {\n            // Add cache-busting parameter to ensure fresh data\n            const timestamp = Date.now();\n            const response = await fetch(\"/api/documents/list?configId=\".concat(configId, \"&_t=\").concat(timestamp), {\n                cache: 'no-store' // Ensure we don't get cached responses\n            });\n            if (response.ok) {\n                var _data_documents;\n                const data = await response.json();\n                const newCount = ((_data_documents = data.documents) === null || _data_documents === void 0 ? void 0 : _data_documents.length) || 0;\n                console.log(\"[Training Page] Document count updated: \".concat(newCount, \" for config: \").concat(configId));\n                setDocumentCount(newCount);\n            } else {\n                console.error('Error fetching document count:', response.status, response.statusText);\n                // Retry once if the request failed\n                if (retryCount < 1) {\n                    setTimeout(()=>fetchDocumentCount(configId, retryCount + 1), 1000);\n                }\n            }\n        } catch (err) {\n            console.error('Error fetching document count:', err);\n            // Retry once if there was an error\n            if (retryCount < 1) {\n                setTimeout(()=>fetchDocumentCount(configId, retryCount + 1), 1000);\n            }\n        }\n    };\n    // Fetch document count when config changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TrainingPage.useEffect\": ()=>{\n            if (selectedConfigId) {\n                fetchDocumentCount(selectedConfigId);\n            }\n        }\n    }[\"TrainingPage.useEffect\"], [\n        selectedConfigId\n    ]);\n    // Process training prompts into structured format\n    const processTrainingPrompts = (prompts)=>{\n        const processed = {\n            system_instructions: '',\n            examples: [],\n            behavior_guidelines: '',\n            general_instructions: ''\n        };\n        const lines = prompts.split('\\n').filter((line)=>line.trim());\n        for (const line of lines){\n            const trimmedLine = line.trim();\n            if (trimmedLine.startsWith('SYSTEM:')) {\n                processed.system_instructions += trimmedLine.replace('SYSTEM:', '').trim() + '\\n';\n            } else if (trimmedLine.startsWith('BEHAVIOR:')) {\n                processed.behavior_guidelines += trimmedLine.replace('BEHAVIOR:', '').trim() + '\\n';\n            } else if (trimmedLine.includes('→') || trimmedLine.includes('->')) {\n                // Parse example: \"User input → Expected response\"\n                const separator = trimmedLine.includes('→') ? '→' : '->';\n                const parts = trimmedLine.split(separator);\n                if (parts.length >= 2) {\n                    const input = parts[0].trim();\n                    const output = parts.slice(1).join(separator).trim();\n                    processed.examples.push({\n                        input,\n                        output\n                    });\n                }\n            } else if (trimmedLine.length > 0) {\n                // General instruction\n                processed.general_instructions += trimmedLine + '\\n';\n            }\n        }\n        return processed;\n    };\n    // Handle training job creation or update\n    const handleStartTraining = async ()=>{\n        if (!selectedConfigId || !trainingPrompts.trim()) {\n            setError('Please select an API configuration and provide training prompts.');\n            return;\n        }\n        // Prevent multiple simultaneous training operations\n        if (isLoading) {\n            console.warn('[Training] Operation already in progress, ignoring duplicate request');\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        setSuccessMessage(null);\n        try {\n            var _customConfigs_find;\n            // Process training prompts\n            const processedPrompts = processTrainingPrompts(trainingPrompts);\n            // Generate a meaningful name based on content\n            const configName = ((_customConfigs_find = customConfigs.find((c)=>c.id === selectedConfigId)) === null || _customConfigs_find === void 0 ? void 0 : _customConfigs_find.name) || 'Unknown Config';\n            // Prepare training job data\n            const trainingJobData = {\n                custom_api_config_id: selectedConfigId,\n                name: \"\".concat(configName, \" Training - \").concat(new Date().toLocaleDateString()),\n                description: \"Training job for \".concat(configName, \" with \").concat(processedPrompts.examples.length, \" examples\"),\n                training_data: {\n                    processed_prompts: processedPrompts,\n                    raw_prompts: trainingPrompts.trim(),\n                    last_prompt_update: new Date().toISOString()\n                },\n                parameters: {\n                    training_type: 'prompt_engineering',\n                    created_via: 'training_page',\n                    version: '1.0'\n                }\n            };\n            // Use UPSERT to handle both create and update scenarios\n            const response = await fetch('/api/training/jobs/upsert', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(trainingJobData)\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('[Training] Failed to upsert training job:', errorText);\n                throw new Error(\"Failed to save training job: \".concat(response.status, \" \").concat(errorText));\n            }\n            const result = await response.json();\n            const isUpdate = result.operation === 'updated';\n            console.log(\"[Training] Successfully \".concat(isUpdate ? 'updated' : 'created', \" training job:\"), result.id);\n            // Show success message based on operation type\n            const operationText = isUpdate ? 'updated' : 'enhanced';\n            const operationEmoji = isUpdate ? '🔄' : '🎉';\n            const successMessage = \"\".concat(operationEmoji, \" Prompt Engineering \").concat(isUpdate ? 'updated' : 'completed', \" successfully!\\n\\n\") + 'Your \"'.concat(configName, '\" configuration has been ').concat(operationText, \" with:\\n\") + \"• \".concat(processedPrompts.examples.length, \" training examples\\n\") + \"• Custom system instructions and behavior guidelines\\n\" + \"\\n✨ All future chats using this configuration will automatically:\\n\" + \"• Follow your training examples\\n\" + \"• Apply your behavior guidelines\\n\" + \"• Maintain consistent personality and responses\\n\\n\" + \"\\uD83D\\uDE80 Try it now in the Playground to see your \".concat(isUpdate ? 'updated' : 'enhanced', \" model in action!\\n\\n\") + \"\\uD83D\\uDCA1 Your training prompts remain here so you can modify them anytime.\";\n            setSuccessMessage(successMessage);\n        } catch (err) {\n            console.error('Error in training operation:', err);\n            setError(\"Failed to create prompt engineering: \".concat(err.message));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-800/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-semibold text-white\",\n                                    children: \"Training\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: subscriptionStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__.TierBadge, {\n                                    tier: subscriptionStatus.tier,\n                                    size: \"lg\",\n                                    theme: \"dark\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                lineNumber: 244,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full px-4 sm:px-6 lg:px-8 pb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 bg-red-900/20 border border-red-500/30 rounded-xl p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 text-red-400\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-200 text-sm font-medium\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, this),\n                        successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 bg-green-900/20 border border-green-500/30 rounded-xl p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 text-green-400\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M5 13l4 4L19 7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-200 text-sm font-medium\",\n                                        children: successMessage\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__.TierGuard, {\n                            feature: \"knowledge_base\",\n                            customMessage: \"Knowledge base document upload is available starting with the Professional plan. Upload documents to enhance your AI with proprietary knowledge and create domain-specific assistants.\",\n                            theme: \"dark\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-8 mb-8 border border-gray-800/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-orange-500/20 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 text-orange-400\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-white\",\n                                                        children: \"Knowledge Documents\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: \"Upload documents to enhance your AI with proprietary knowledge\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 13\n                                    }, this),\n                                    subscriptionStatus && selectedConfigId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4 bg-gray-800/50 border border-gray-700/50 rounded-lg px-4 py-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__.LimitIndicator, {\n                                            current: documentCount,\n                                            limit: subscriptionStatus.tier === 'professional' ? 5 : subscriptionStatus.tier === 'enterprise' ? 999999 : 0,\n                                            label: \"Knowledge Base Documents\",\n                                            tier: subscriptionStatus.tier,\n                                            showUpgradeHint: true,\n                                            theme: \"dark\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DocumentUpload__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        configId: selectedConfigId,\n                                        theme: \"dark\",\n                                        onDocumentUploaded: ()=>{\n                                            console.log('Document uploaded successfully');\n                                            // Add a small delay to ensure database consistency before refreshing count\n                                            if (selectedConfigId) {\n                                                setTimeout(()=>{\n                                                    fetchDocumentCount(selectedConfigId);\n                                                }, 500); // 500ms delay\n                                            }\n                                        },\n                                        onDocumentDeleted: ()=>{\n                                            console.log('Document deleted successfully');\n                                            // Add a small delay to ensure database consistency before refreshing count\n                                            if (selectedConfigId) {\n                                                setTimeout(()=>{\n                                                    fetchDocumentCount(selectedConfigId);\n                                                }, 500); // 500ms delay\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__.TierGuard, {\n                            feature: \"prompt_engineering\",\n                            customMessage: \"Prompt engineering is available starting with the Starter plan. Create custom prompts to define AI behavior, provide examples, and enhance your model's responses.\",\n                            theme: \"dark\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-8 border border-gray-800/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 text-blue-400\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-white\",\n                                                        children: \"Custom Prompts\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: \"Define behavior, examples, and instructions for your AI\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"configSelect\",\n                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                        children: \"Select API Configuration\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"configSelect\",\n                                                        value: selectedConfigId,\n                                                        onChange: (e)=>setSelectedConfigId(e.target.value),\n                                                        className: \"w-full px-4 py-3 border border-gray-700 rounded-xl focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 transition-colors bg-gray-800 text-white text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"Choose which model to train...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            customConfigs.map((config)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: config.id,\n                                                                    children: config.name\n                                                                }, config.id, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                    lineNumber: 379,\n                                                                    columnNumber: 19\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"trainingPrompts\",\n                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                        children: \"Custom Prompts & Instructions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        id: \"trainingPrompts\",\n                                                        value: trainingPrompts,\n                                                        onChange: (e)=>setTrainingPrompts(e.target.value),\n                                                        placeholder: \"Enter your training prompts using these formats:\\n\\nSYSTEM: You are a helpful customer service agent for our company\\nBEHAVIOR: Always be polite and offer solutions\\n\\nUser asks about returns → I'd be happy to help with your return! Let me check our policy for you.\\nCustomer is frustrated → I understand your frustration. Let me see how I can resolve this for you.\\n\\nGeneral instructions can be written as regular text.\",\n                                                        rows: 12,\n                                                        className: \"w-full px-4 py-3 border border-gray-700 rounded-xl focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 transition-colors bg-gray-800 text-white text-sm resize-none font-mono placeholder-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-3 bg-blue-900/20 border border-blue-500/30 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-blue-300 mb-2\",\n                                                                children: \"Training Format Guide:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                lineNumber: 408,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"text-xs text-blue-200 space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"SYSTEM:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                                lineNumber: 410,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            \" Core instructions for the AI's role and personality\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                        lineNumber: 410,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"BEHAVIOR:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                                lineNumber: 411,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            \" Guidelines for how the AI should behave\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                        lineNumber: 411,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"Examples:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                                lineNumber: 412,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            ' Use \"User input → Expected response\" format'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                        lineNumber: 412,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"General:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                                lineNumber: 413,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            \" Any other instructions written as normal text\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                        lineNumber: 413,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center pt-6 border-t border-gray-700/50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            className: \"btn-secondary\",\n                                                            onClick: ()=>{\n                                                                confirmation.showConfirmation({\n                                                                    title: 'Clear Training Prompts',\n                                                                    message: 'Are you sure you want to clear all training prompts? This will remove all your custom instructions, examples, and behavior guidelines. This action cannot be undone.',\n                                                                    confirmText: 'Clear All',\n                                                                    cancelText: 'Cancel',\n                                                                    type: 'warning'\n                                                                }, ()=>{\n                                                                    setTrainingPrompts('');\n                                                                });\n                                                            },\n                                                            children: \"Clear Form\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: handleStartTraining,\n                                                        disabled: !selectedConfigId || !trainingPrompts.trim() || isLoading,\n                                                        className: \"btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                    lineNumber: 453,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"Processing Prompts...\"\n                                                            ]\n                                                        }, void 0, true) : 'Save Prompts'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            isOpen: confirmation.isOpen,\n                            onClose: confirmation.hideConfirmation,\n                            onConfirm: confirmation.onConfirm,\n                            title: confirmation.title,\n                            message: confirmation.message,\n                            confirmText: confirmation.confirmText,\n                            cancelText: confirmation.cancelText,\n                            type: confirmation.type,\n                            isLoading: confirmation.isLoading\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                lineNumber: 261,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n        lineNumber: 242,\n        columnNumber: 5\n    }, this);\n}\n_s(TrainingPage, \"UrnJmCfw8iCy300JBOY9o5vKe/8=\", false, function() {\n    return [\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_4__.useSubscription,\n        _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_5__.useConfirmation\n    ];\n});\n_c = TrainingPage;\nvar _c;\n$RefreshReg$(_c, \"TrainingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/training/page.tsx\n"));

/***/ })

});